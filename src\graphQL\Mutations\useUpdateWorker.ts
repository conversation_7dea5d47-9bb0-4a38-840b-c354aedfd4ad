import { useMutation } from '@vue/apollo-composable'
import gql from 'graphql-tag'

export type UpdateWorkerReturnType = {
  id: number
  name: string
  company: string
  phoneNumber: string
  trade: string
  skills: string[]
  nationalId: string
  gender: string
  age: number
  trainingIds: number[]
  manHours: number
  rating: number
  createdBy: string
  createdAt: string // DateTime is usually represented as a string in GraphQL responses
  photoUrl?: string | null
  email?: string | null
  inductionDate?: string | null
  medicalCheckDate?: string | null
  updatedAt?: string | null
  updatedBy?: string | null
}

export type UpdateWorkerParameterType = {
  id: number
  name?: string
  company?: string
  trade?: string
  // skill?: string | null
  skills?: string[]
  trainingIds?: number[]
  // trainingsCompleted?: number | null
  manHours?: number
  rating?: number
  gender?: string
  phoneNumber?: string
  email?: string
  inductionDate?: Date
  medicalCheckDate?: Date
}

export function toUpdateWorkerParameter(data: UpdateWorkerReturnType): UpdateWorkerParameterType {
  return {
    id: data.id,
    name: data.name,
    company: data.company,
    trade: data.trade,
    skills: data.skills,
    trainingIds: data.trainingIds,
    manHours: data.manHours,
    rating: data.rating,
    gender: data.gender,
    phoneNumber: data.phoneNumber,
    email: data.email ?? undefined,
    inductionDate: data.inductionDate ? new Date(data.inductionDate) : undefined,
    medicalCheckDate: data.medicalCheckDate ? new Date(data.medicalCheckDate) : undefined,
  }
}

export function useUpdateWorker() {
  const updateWorkerQL = gql`
    mutation UpdateWorker(
      $id: Int!
      $name: String
      $company: String
      $trade: String
      $skill: String
      $trainingsCompleted: Int
      $manHours: Int
      $rating: Float
      $gender: String
      $phoneNumber: String
      $email: String
      $inductionDate: DateTime
      $medicalCheckDate: DateTime
    ) {
      updateWorker(
        id: $id
        name: $name
        company: $company
        trade: $trade
        skill: $skill
        trainingsCompleted: $trainingsCompleted
        manHours: $manHours
        rating: $rating
        gender: $gender
        phoneNumber: $phoneNumber
        email: $email
        inductionDate: $inductionDate
        medicalCheckDate: $medicalCheckDate
      ) {
        id
        name
        company
        trade
        skill
        trainingsCompleted
        manHours
        rating
        gender
        phoneNumber
        email
        inductionDate
        medicalCheckDate
      }
    }
  `
  return useMutation<UpdateWorkerParameterType>(updateWorkerQL)
}
