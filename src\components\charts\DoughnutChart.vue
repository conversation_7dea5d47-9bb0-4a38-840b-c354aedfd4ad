<style scoped lang="scss">
.card {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}
.chart {
  width: 16rem;
}
.chart-key {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom-color: val(--p-text-color);
  border-bottom: 2px solid;
  padding-bottom: 0.5rem;
}
</style>
<template>
  <Card style="overflow: hidden">
    <template #title>
      <p style="font-weight: 900; font-size: 1.6rem">
        {{ 'People' }}
      </p>
    </template>
    <template #content>
      <div class="card">
        <Chart type="doughnut" class="chart" :data="chartData" :options="chartOptions" />
        <div style="display: flex; flex-direction: column; gap: 1rem; margin-top: 1rem">
          <p v-for="(val, idx) in chartSummary" :key="idx" class="chart-key">
            <span
              :style="{ backgroundColor: val.color }"
              style="width: 1.5rem; height: 1.5rem; display: block; border-radius: 4px"
            ></span>
            <span>{{ val.label }}</span>
            <span style="margin-left: auto">{{ val.percentData }}%</span>
          </p>
        </div>
      </div>
    </template>
  </Card>
</template>
<script setup lang="ts">
import Chart from 'primevue/chart'
import { Card } from 'primevue'
import { ref, onMounted } from 'vue'
import { generateColorValues } from '@/utils/coloValues'

onMounted(() => {
  const data = setChartData()
  chartSummary.value = createChartDetails()
  chartData.value = data
  chartOptions.value = setChartOptions()
})

const chartData = ref()
const chartOptions = ref()
const chartSummary = ref()

type ChartDataType = {
  label: string
  data: number
}

const chartSourceData: ChartDataType[] = [
  {
    label: 'Present',
    data: 54,
  },
  {
    label: 'Late',
    data: 32,
  },
  {
    label: 'Toolbox',
    data: 70,
  },
  {
    label: 'Late',
    data: 50,
  },
]
const chartColors = chartSourceData.map(() => generateColorValues())

function createChartDetails() {
  const documentStyle = getComputedStyle(document.body)
  const total = chartSourceData.reduce((acc, current) => acc + current.data, 0)

  // console.log(total)
  const colorDetail = chartSourceData.map((i, idx) => ({
    label: i.label,
    color: documentStyle.getPropertyValue(`--p-${chartColors[idx][0]}-${chartColors[idx][2]}`),
    percentData: Math.round((i.data / total) * 100),
  }))
  return colorDetail
}

const setChartData = () => {
  const documentStyle = getComputedStyle(document.body)

  //   console.log(chartColors)
  return {
    // labels: ['A', 'B', 'C'],
    datasets: [
      {
        data: chartSourceData.map((i) => i.data),
        backgroundColor: chartColors.map((i) =>
          documentStyle.getPropertyValue(`--p-${i[0]}-${i[2]}`),
        ),
        hoverBackgroundColor: chartColors.map((i) =>
          documentStyle.getPropertyValue(`--p-${i[0]}-${i[1]}`),
        ),
        borderColor: 'transparent',
      },
    ],
  }
}

const setChartOptions = () => {
  //   const documentStyle = getComputedStyle(document.documentElement)
  //   const textColor = documentStyle.getPropertyValue('--p-text-color')

  return {
    plugins: {
      legend: {
        labels: {
          // cutout: '60%',
          // color: textColor,
        },
      },
    },
  }
}
</script>
