import { useMutation } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import type { SkillType } from '../graphqlTypes'

export type CreateType = {
  name: string
  description: string | null
  // workerIds: number[] | null
}

export function useCreateSkill() {
  const query = gql`
    mutation CreateSkill($name: String!, $description: String) # $workerIds: [Int!]
    {
      createSkill(name: $name, description: $description) # workerIds: $workerIds
      {
        #id
        name
        #description
        #createdAt
        #createdBy
        #updatedAt
        #updatedBy
        #workers {
        #  id
        #  name
        #  company
        #}
      }
    }
  `
  return useMutation<{ createSkill: SkillType }>(query)
}
