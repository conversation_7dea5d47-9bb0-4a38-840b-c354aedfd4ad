import { useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { type TradeType } from '../graphqlTypes'

export function useGetAllTrades() {
  const query = gql`
    query GetAllTrades {
      allTrades {
        id
        name
        description
        workers {
          id
          name
          company
        }
        createdAt
        createdBy
        updatedAt
        updatedBy
      }
    }
  `
  return useQuery<{ allTrades: TradeType[] }>(query)
}
