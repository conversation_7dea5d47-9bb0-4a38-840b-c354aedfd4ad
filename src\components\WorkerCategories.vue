<style scoped lang="scss"></style>

<template>
  <!-- <div class="wrapper-tree"> -->
  <Card
    class="wrapper-tree"
    :pt="{
      content: {
        style: {
          display: 'flex',
        },
      },
    }"
  >
    <template #content>
      <template v-for="item in nodes" :key="item.key">
        <Tree
          v-model:selectionKeys="selectedKey"
          :value="[item]"
          selectionMode="checkbox"
          class="tree"
        >
        </Tree>
      </template>
    </template>
  </Card>
  <!-- <Tree
      v-model:selectionKeys="selectedKey"
      :value="nodes"
      selectionMode="checkbox"
      class="tree"
    ></Tree> -->
  <!-- </div> -->
</template>
<script setup lang="ts">
import { Tree, Card } from 'primevue'
import { ref, onMounted } from 'vue'

const nodes = ref()
const selectedKey = ref()

const categories = [
  {
    key: '7247e15d-63d8-4b96-b20a-c3266e44f4c3',
    label: 'Projects',
    data: 'Project Folder',
    children: [
      {
        key: '878f2678-fc12-4a07-bcaa-775528faadd0',
        label: 'Safaricom Project',
        data: 'Safaricom Project',
      },
      {
        key: 'b4ed53e6-429d-4e92-a2e2-73de1209cef6',
        label: 'Mall Project',
        data: 'Mall Project',
      },
      {
        key: 'b4f25dbb-3fcc-477f-87f0-28bb79c18057',
        label: 'Data Center Project',
        data: 'Data Center Project',
      },
    ],
  },
  // {
  //   key: 'adf1de61-251f-43e9-b35d-347a5c7764cb',
  //   label: 'Register',
  //   data: 'Register Folder',
  //   children: [
  //     {
  //       key: 'e5b13d3b-baab-4361-864e-09d2929fd35a',
  //       label: 'Finishing',
  //       data: 'Finishing Register',
  //     },
  //     {
  //       key: 'e97b321e-e7f8-43ac-a77b-de1c7d65d1e8',
  //       label: 'Inspection',
  //       data: 'Inspection Register',
  //     },
  //     {
  //       key: 'b44c1240-aa5c-4d76-b7fe-1efd0dd4218f',
  //       label: 'Probation',
  //       data: 'Probation Register',
  //     },
  //     {
  //       key: '3836c250-0443-41be-a173-f16609c9c6ea',
  //       label: 'Mantainance',
  //       data: 'Mantainance Register',
  //     },
  //   ],
  // },
  {
    key: '41599d1f-2c04-48c1-b87a-f8c3d040bd27',
    label: 'Assign Tasks',
    data: 'Assign Tasks Folder',
    children: [
      {
        key: '961c100c-2ee8-4a6b-a553-8d36961f4dd0',
        label: 'Cleaning',
        data: 'Cleaning Task',
      },
      {
        key: 'b3fee3eb-31fc-4a9f-b0f9-c9ee8adae56b',
        label: 'Cement Mixing',
        data: 'Cement Mixing Task',
      },
      {
        key: '0758df42-a03d-44dd-bf4d-d08e6b66fe6f',
        label: 'Security',
        data: 'Security Task',
      },
      {
        key: '6044b2e0-1d3a-47d8-8998-b1006ea0d2f0',
        label: 'Management',
        data: 'Management Task',
      },
    ],
  },
  {
    key: 'f497eca9-1ad5-4f86-a827-2f0bd59d89c9',
    label: 'Schedule Training',
    data: 'Schedule Training Folder',
    children: [
      {
        key: 'e8a23685-171a-4d2b-a606-994bef7cadea',
        label: 'Safety',
        data: 'Safety Training',
      },
      {
        key: '0be976c9-aa10-4144-b8a9-154743e42dba',
        label: 'Management',
        data: 'Management Training',
      },
      {
        key: 'f6875578-81b7-4266-8a0b-01c21215deeb',
        label: 'Accounting',
        data: 'Accounting Training',
      },
    ],
  },
]

onMounted(() => {
  nodes.value = categories
})
</script>
