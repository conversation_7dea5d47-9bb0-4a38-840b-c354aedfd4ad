import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
// import MainPage from '../views/home/<USER>'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: HomeView,
      children: [
        {
          path: '',
          // component: MainPage,
          component: () => import('../views/home/<USER>'),
          name: 'home',
        },
      ],
    },
    {
      path: '/dashboard',
      component: () => import('../views/DashView.vue'),
      children: [
        {
          path: '',
          component: () => import('../views/dashboard/DashHome.vue'),
          name: 'dashHome',
        },
        {
          path: 'attendance',
          component: () => import('../views/dashboard/AttendanceView.vue'),
          name: 'attendance',
        },
        {
          path: 'toolbox',
          component: () => import('../views/dashboard/ToolboxView.vue'),
          name: 'toolbox',
        },
        {
          path: 'overtime',
          component: () => import('../views/dashboard/OvertimeView.vue'),
          name: 'overtime',
        },
        {
          path: 'request-overtime',
          component: () => import('../views/dashboard/OvertimeRequest.vue'),
          name: 'requestOvertime',
        },
        {
          path: 'workers',
          component: () => import('../views/dashboard/WorkerPage.vue'),
          name: 'workers',
        },
        {
          path: 'create-worker',
          component: () => import('../views/dashboard/CreateWorkerView.vue'),
          name: 'createWorker',
        },
        {
          path: 'forms',
          component: () => import('../layouts/BasicFormLayout.vue'),
          children: [
            {
              path: 'inspection/:id',
              component: () => import('../views/dashboard/InspectionForm.vue'),
              name: 'inspectionForm',
            },
          ],
        },
        {
          path: 'update-worker/:id',
          component: () => import('../views/dashboard/UpdateWorkerView.vue'),
          name: 'updateWorker',
        },
        {
          path: 'worker-profile/:id',
          component: () => import('@/views/dashboard/WorkerProfile.vue'),
          name: 'workerProfile',
        },
        {
          path:'trainings',
          component: ()=> import('@/views/dashboard/TrainingView.vue'),
          name:"trainings"
        },
        {
          path:"training-schedule",
          component: ()=> import('@/views/dashboard/TrainingSchedule.vue'),
          name:"trainingSchedule"
        },
      ],
    },
  ],
})

export default router
