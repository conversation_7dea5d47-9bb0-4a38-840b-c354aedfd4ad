<template>
  <div class="wrapper">
    <BasicSidebar v-if="isBasicSidebarOpen" />
    <div class="main">
      <NavBar :title="title" />
      <div style="height: 100%">
        <slot />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import NavBar from '@/components/NavBar.vue'
import BasicSidebar from '../components/BasicSidebar.vue'
import { useLayoutStore } from '@/stores/LayoutStore'
import { storeToRefs } from 'pinia'

const layoutStore = useLayoutStore()
const { isBasicSidebarOpen } = storeToRefs(layoutStore)

const { title = '' } = defineProps<{
  title?: string
}>()
</script>

<style scoped lang="scss">
.wrapper {
  height: 100vh;
  // width: 100vw;
  display: flex;
  gap: 0.5rem;
  .main {
    width: 100%;
    padding-block: 1rem;
    display: flex;
    flex-direction: column;
  }
}
</style>
