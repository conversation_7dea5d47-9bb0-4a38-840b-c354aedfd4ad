<template>
  <Dialog v-model:visible="visible" modal header="Create New Training" :style="{ width: '50rem' }">
    <form @submit.prevent="handleSubmit" class="training-form">
      <div class="form-grid">
        <!-- Basic Information -->
        <div class="form-section">
          <h4>Basic Information</h4>
          
          <div class="field">
            <label for="name">Training Name *</label>
            <InputText 
              id="name" 
              v-model="formData.name" 
              :class="{ 'p-invalid': errors.name }" 
              placeholder="Enter training name"
            />
            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
          </div>

          <div class="field">
            <label for="description">Description</label>
            <Textarea 
              id="description" 
              v-model="formData.description" 
              rows="3" 
              placeholder="Enter training description"
            />
          </div>

          <div class="field">
            <label for="trainingType">Training Type</label>
            <InputText 
              id="trainingType" 
              v-model="formData.trainingType" 
              placeholder="e.g., Safety, Technical, Compliance"
            />
          </div>

          <div class="field">
            <label for="trainer">Trainer</label>
            <InputText 
              id="trainer" 
              v-model="formData.trainer" 
              placeholder="Enter trainer name"
            />
          </div>
        </div>

        <!-- Schedule Information -->
        <div class="form-section">
          <h4>Schedule Information</h4>
          
          <div class="field">
            <label for="startDate">Start Date</label>
            <Calendar 
              id="startDate" 
              v-model="formData.startDate" 
              showTime 
              hourFormat="24"
              placeholder="Select start date and time"
            />
          </div>

          <div class="field">
            <label for="endDate">End Date</label>
            <Calendar 
              id="endDate" 
              v-model="formData.endDate" 
              showTime 
              hourFormat="24"
              placeholder="Select end date and time"
            />
          </div>

          <div class="field">
            <label for="duration">Duration</label>
            <InputText 
              id="duration" 
              v-model="formData.duration" 
              placeholder="e.g., 2 hours, 1 day"
            />
          </div>

          <div class="field">
            <label for="frequency">Frequency</label>
            <InputText 
              id="frequency" 
              v-model="formData.frequency" 
              placeholder="e.g., Annual, Monthly, One-time"
            />
          </div>
        </div>

        <!-- Certification Information -->
        <div class="form-section">
          <h4>Certification</h4>
          
          <div class="field">
            <label for="validityPeriod">Validity Period (Months)</label>
            <InputNumber 
              id="validityPeriod" 
              v-model="formData.validityPeriodMonths" 
              :min="1" 
              :max="120"
              placeholder="Enter validity period in months"
            />
          </div>

          <div class="field">
            <label for="status">Status</label>
            <Dropdown 
              id="status" 
              v-model="formData.status" 
              :options="statusOptions" 
              optionLabel="label" 
              optionValue="value"
              placeholder="Select training status"
            />
          </div>
        </div>
      </div>

      <div class="form-actions">
        <Button type="button" label="Cancel" severity="secondary" @click="handleCancel" />
        <Button type="submit" label="Create Training" :loading="loading" />
      </div>
    </form>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Calendar from 'primevue/calendar'
import InputNumber from 'primevue/inputnumber'
import Dropdown from 'primevue/dropdown'
import Button from 'primevue/button'
import { useCreateTraining, type CreateTrainingType } from '@/graphQL/Mutations/useCreateTraining'
import { TrainingStatus } from '@/graphQL/graphqlTypes'
import { useToast } from 'primevue/usetoast'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'training-created'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const toast = useToast()
const { mutate: createTraining, loading, onDone, onError } = useCreateTraining()

const formData = reactive<CreateTrainingType>({
  name: '',
  description: '',
  trainingType: '',
  trainer: '',
  startDate: null,
  endDate: null,
  duration: '',
  frequency: '',
  validityPeriodMonths: null,
  status: TrainingStatus.Scheduled,
  workerIds: []
})

const errors = reactive({
  name: ''
})

const statusOptions = [
  { label: 'Scheduled', value: TrainingStatus.Scheduled },
  { label: 'Active', value: TrainingStatus.Active },
  { label: 'Completed', value: TrainingStatus.Completed },
  { label: 'Cancelled', value: TrainingStatus.Cancelled }
]

const visible = ref(props.visible)

watch(() => props.visible, (newValue) => {
  visible.value = newValue
  if (newValue) {
    resetForm()
  }
})

watch(visible, (newValue) => {
  emit('update:visible', newValue)
})

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    trainingType: '',
    trainer: '',
    startDate: null,
    endDate: null,
    duration: '',
    frequency: '',
    validityPeriodMonths: null,
    status: TrainingStatus.Scheduled,
    workerIds: []
  })
  errors.name = ''
}

const validateForm = () => {
  errors.name = ''
  
  if (!formData.name || formData.name.trim().length < 3) {
    errors.name = 'Training name must be at least 3 characters long'
    return false
  }
  
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  const submitData = {
    ...formData,
    startDate: formData.startDate ? formData.startDate.toISOString() : null,
    endDate: formData.endDate ? formData.endDate.toISOString() : null,
  }

  await createTraining(submitData)
}

const handleCancel = () => {
  visible.value = false
}

onDone(() => {
  toast.add({
    severity: 'success',
    summary: 'Success',
    detail: 'Training created successfully',
    life: 3000
  })
  visible.value = false
  emit('training-created')
})

onError((error) => {
  toast.add({
    severity: 'error',
    summary: 'Error',
    detail: error.message || 'Failed to create training',
    life: 5000
  })
})
</script>

<style scoped lang="scss">
.training-form {
  .form-grid {
    display: grid;
    gap: 2rem;
  }

  .form-section {
    h4 {
      margin: 0 0 1rem 0;
      color: var(--primary-color);
      border-bottom: 1px solid var(--surface-border);
      padding-bottom: 0.5rem;
    }
  }

  .field {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .p-inputtext,
    .p-dropdown,
    .p-calendar,
    .p-inputnumber {
      width: 100%;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--surface-border);
  }
}
</style>
