<template>
  <Card>
    <template #title>
      <h3 style="font-weight: 900; font-size: 1.6rem">Site Overview</h3>
    </template>
    <template #content>
      <DataTable :value="tableData" tableStyle="min-width: 25rem" showGridlines stripedRows>
        <Column field="state" header=""></Column>
        <Column field="value" header=""></Column>
      </DataTable>
    </template>
  </Card>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { Card } from 'primevue'

const data = [
  {
    state: 'Compliance',
    value: 'Foundation',
  },
  {
    state: 'Safety',
    value: 'Next Milestone',
  },
  {
    state: 'Equipment',
    value: 'Man Hours Logged',
  },
  {
    state: 'Start and End Dates',
    value: new Date().toDateString(),
  },
  {
    state: 'Project',
    value: '<PERSON>',
  },
]

const tableData = ref(data)
</script>
