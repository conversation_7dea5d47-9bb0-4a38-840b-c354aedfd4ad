<style scoped lang="scss">
.item {
  display: flex;
  gap: 0.4rem;
  align-items: center;
}
</style>

<template>
  <div class="item">
    <Checkbox
      @update:modelValue="(val: boolean) => handler(val)"
      v-model="value"
      :inputId="inputId"
      binary
      :value="value"
      :name="inputId"
    />
    <label :for="inputId">{{ value ? 'Yes' : 'No' }}</label>
  </div>
</template>
<script setup lang="ts">
import { Checkbox } from 'primevue'
import { ref } from 'vue'

const value = ref<boolean>(false)
const props = defineProps<{ id: string }>()
const inputId = `${props.id}`

const emit = defineEmits<{
  change: [value: boolean]
}>()

const handler = (val: boolean) => {
  emit('change', val)
}
</script>
