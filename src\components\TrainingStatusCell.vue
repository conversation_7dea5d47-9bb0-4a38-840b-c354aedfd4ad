<template>
  <div class="training-status-cell" :class="statusClass" @click="handleClick">
    <div class="status-content">
      <i :class="statusIcon" class="status-icon"></i>
      <span class="status-text">{{ statusText }}</span>
    </div>
    <div v-if="showDetails" class="status-details">
      <small v-if="completionDate">Completed: {{ formatDate(completionDate) }}</small>
      <small v-if="expiryDate">Expires: {{ formatDate(expiryDate) }}</small>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { TrainingStatus } from '@/graphQL/graphqlTypes'

interface Props {
  status: TrainingStatus | null
  completionDate?: string | null
  expiryDate?: string | null
  interactive?: boolean
  showDetails?: boolean
}

interface Emits {
  (e: 'status-change', status: TrainingStatus): void
}

const props = withDefaults(defineProps<Props>(), {
  interactive: false,
  showDetails: false
})

const emit = defineEmits<Emits>()

const statusClass = computed(() => {
  const baseClass = 'training-status'
  const statusSpecificClass = props.status ? `status-${props.status.toLowerCase()}` : 'status-none'
  const interactiveClass = props.interactive ? 'interactive' : ''
  
  return [baseClass, statusSpecificClass, interactiveClass].filter(Boolean).join(' ')
})

const statusIcon = computed(() => {
  switch (props.status) {
    case TrainingStatus.Completed:
      return 'pi pi-check-circle'
    case TrainingStatus.Scheduled:
      return 'pi pi-clock'
    case TrainingStatus.Active:
      return 'pi pi-play-circle'
    case TrainingStatus.Expired:
      return 'pi pi-exclamation-triangle'
    case TrainingStatus.Cancelled:
      return 'pi pi-times-circle'
    default:
      return 'pi pi-minus-circle'
  }
})

const statusText = computed(() => {
  switch (props.status) {
    case TrainingStatus.Completed:
      return 'Done'
    case TrainingStatus.Scheduled:
      return 'Scheduled'
    case TrainingStatus.Active:
      return 'Active'
    case TrainingStatus.Expired:
      return 'Expired'
    case TrainingStatus.Cancelled:
      return 'Cancelled'
    default:
      return 'Not Assigned'
  }
})

const handleClick = () => {
  if (!props.interactive) return
  
  // Cycle through relevant statuses for interactive mode
  const nextStatus = props.status === TrainingStatus.Scheduled 
    ? TrainingStatus.Completed 
    : TrainingStatus.Scheduled
    
  emit('status-change', nextStatus)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped lang="scss">
.training-status-cell {
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
  min-height: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.2s ease;

  &.interactive {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .status-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    
    .status-icon {
      font-size: 0.875rem;
    }
    
    .status-text {
      font-size: 0.75rem;
      font-weight: 600;
      white-space: nowrap;
    }
  }

  .status-details {
    margin-top: 0.25rem;
    
    small {
      display: block;
      font-size: 0.625rem;
      opacity: 0.8;
    }
  }

  // Status-specific colors
  &.status-completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.status-scheduled {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  &.status-active {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
  }

  &.status-expired {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  &.status-cancelled {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
  }

  &.status-none {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
  }
}
</style>
