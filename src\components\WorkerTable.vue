<style lang="scss" scoped>
.text-green-500 {
  color: var(--p-green-500);
}
.text-red-500 {
  color: var(--p-red-500);
}
.wrap {
  width: 100%;
  height: 100%;
}
.search {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <!-- :size="isBasicSidebarOpen || screenSize < 1300 ? 'small' : undefined" -->
  <div class="wrap">
    <DataTable
      v-model:filters="filters"
      :value="tableData"
      paginator
      showGridlines
      :rows="10"
      dataKey="id"
      filterDisplay="menu"
      :loading="allWorkerLoading"
      v-model:selection="selectedItem"
      :globalFilterFields="['name', 'gender', 'nationalId', 'phoneNumber']"
      :size="'small'"
    >
      <template #header>
        <div class="search">
          <Button
            type="button"
            icon="pi pi-filter-slash"
            label="Clear"
            outlined
            @click="clearFilter()"
          />
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText v-model="filters['global'].value" placeholder="Keyword Search" />
          </IconField>
        </div>
      </template>

      <template #empty> No customers found. </template>

      <template #loading> Loading customers data. Please wait. </template>
      <Column selectionMode="multiple"></Column>

      <Column field="name" header="Name">
        <template #body="{ data }">
          <router-link
            :to="{ name: 'workerProfile', params: { id: data.id } }"
            style="text-decoration: none; color: inherit"
          >
            {{ data.name }}
          </router-link>
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search by name" />
        </template>
      </Column>

      <Column field="gender" header="Gender">
        <template #body="{ data }">
          {{ data.gender }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search gender" />
        </template>
      </Column>
      <Column field="nationalId" header="National Id No">
        <template #body="{ data }">
          {{ data.nationalId }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search by name" />
        </template>
      </Column>

      <Column field="phoneNumber" header="Phone Number">
        <template #body="{ data }">
          {{ data.phoneNumber }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search phoneNumber" />
        </template>
      </Column>
    </DataTable>
  </div>
</template>
<script setup lang="ts">
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { InputIcon, InputText, IconField, Button } from 'primevue'
import { ref, onMounted } from 'vue'
import { FilterMatchMode, FilterOperator } from '@primevue/core/api'
// import { useLayoutStore } from '@/stores/LayoutStore'
// import { storeToRefs } from 'pinia'
import { type GetAllWorkersReturnType, useGetAllWorkers } from '@/graphQL/Queries/useGetAllWorkers'
import { useToast } from 'primevue'

// type GenderType = 'M' | 'F'
type GenderType = 'Male' | 'Female'

type WorkerTableType = {
  id: number
  name: string
  nationalId: string
  company: string
  trade: string
  trainingsCompleted: number
  skill: string
  manHours: number
  gender: GenderType
  rating: number
  phoneNumber?: string | null
}

function convertToWorkerTable(data: GetAllWorkersReturnType): WorkerTableType[] {
  return data.map((worker) => ({
    id: worker.id,
    name: worker.name,
    nationalId: worker.nationalId,
    company: worker.company,
    trade: worker.trade,
    trainingsCompleted: worker.trainingsCompleted,
    skill: worker.skill,
    manHours: worker.manHours,
    gender: (worker.gender as GenderType) || 'Male', // Ensure GenderType mapping
    rating: worker.rating,
    phoneNumber: worker.phoneNumber,
  }))
}

// const workerTableData: WorkerTableType[] = [
//   {
//     name: 'John Smith',
//     trade: 'Carpentry',
//     training: 'Journeyman Carpenter',
//     skill: ['Framing', 'Finish Work', 'Cabinet Installation'],
//     manHours: 2450,
//     gender: 'M',
//     rating: 4.8,
//   },
//   {
//     name: 'Maria Garcia',
//     trade: 'Electrical',
//     training: 'Master Electrician',
//     skill: ['Wiring', 'Panel Installation', 'Troubleshooting'],
//     manHours: 3200,
//     gender: 'F',
//     rating: 4.9,
//   },
//   {
//     name: 'Robert Johnson',
//     trade: 'Plumbing',
//     training: 'Licensed Plumber',
//     skill: ['Pipe Fitting', 'Fixture Installation', 'Drainage Systems'],
//     manHours: 1980,
//     gender: 'M',
//     rating: 4.6,
//   },
//   {
//     name: 'Emily Chen',
//     trade: 'Project Management',
//     training: 'PMP Certified',
//     skill: ['Scheduling', 'Budget Management', 'Client Relations'],
//     manHours: 4100,
//     gender: 'F',
//     rating: 4.7,
//   },
//   {
//     name: 'David Williams',
//     trade: 'Masonry',
//     training: 'Certified Mason',
//     skill: ['Bricklaying', 'Stone Work', 'Concrete Finishing'],
//     manHours: 2800,
//     gender: 'M',
//     rating: 4.5,
//   },
//   {
//     name: 'Sarah Martinez',
//     trade: 'HVAC',
//     training: 'HVAC Technician',
//     skill: ['Installation', 'Maintenance', 'Diagnostics'],
//     manHours: 1650,
//     gender: 'F',
//     rating: 4.8,
//   },
//   {
//     name: 'Michael Brown',
//     trade: 'Welding',
//     training: 'Certified Welder',
//     skill: ['MIG', 'TIG', 'Structural Welding'],
//     manHours: 2250,
//     gender: 'M',
//     rating: 4.7,
//   },
//   {
//     name: 'Jennifer Taylor',
//     trade: 'Safety',
//     training: 'OSHA Certified',
//     skill: ['Inspections', 'Training', 'Compliance'],
//     manHours: 1875,
//     gender: 'F',
//     rating: 4.9,
//   },
//   {
//     name: 'Thomas Anderson',
//     trade: 'Heavy Equipment',
//     training: 'Equipment Operator License',
//     skill: ['Excavator', 'Bulldozer', 'Crane'],
//     manHours: 3050,
//     gender: 'M',
//     rating: 4.6,
//   },
//   {
//     name: 'Lisa Rodriguez',
//     trade: 'Interior Design',
//     training: 'NCIDQ Certified',
//     skill: ['Space Planning', 'Material Selection', 'Rendering'],
//     manHours: 2100,
//     gender: 'F',
//     rating: 4.8,
//   },
//   {
//     name: 'James Wilson',
//     trade: 'Roofing',
//     training: 'Certified Roofer',
//     skill: ['Shingle Installation', 'Waterproofing', 'Repairs'],
//     manHours: 1950,
//     gender: 'M',
//     rating: 4.3,
//   },
//   {
//     name: 'Patricia Lee',
//     trade: 'Painting',
//     training: 'Professional Painter',
//     skill: ['Interior', 'Exterior', 'Specialty Finishes'],
//     manHours: 1750,
//     gender: 'F',
//     rating: 4.5,
//   },
//   {
//     name: 'Christopher Davis',
//     trade: 'Surveying',
//     training: 'Licensed Surveyor',
//     skill: ['Site Layout', 'Topographic Surveys', 'Boundary Marking'],
//     manHours: 2350,
//     gender: 'M',
//     rating: 4.7,
//   },
//   {
//     name: 'Nancy Miller',
//     trade: 'Documentation',
//     training: 'Technical Writing Certificate',
//     skill: ['Permit Applications', 'Compliance Documentation', 'As-Built Drawings'],
//     manHours: 1500,
//     gender: 'F',
//     rating: 4.6,
//   },
//   {
//     name: 'Daniel Jackson',
//     trade: 'Demolition',
//     training: 'Demolition Specialist',
//     skill: ['Structural Demolition', 'Salvage', 'Hazardous Material Handling'],
//     manHours: 2700,
//     gender: 'M',
//     rating: 4.4,
//   },
//   {
//     name: 'Karen Thompson',
//     trade: 'Flooring',
//     training: 'Flooring Installer',
//     skill: ['Tile', 'Hardwood', 'Laminate'],
//     manHours: 1850,
//     gender: 'F',
//     rating: 4.7,
//   },
//   {
//     name: 'Steven Parker',
//     trade: 'Glazing',
//     training: 'Glazier Certification',
//     skill: ['Window Installation', 'Storefront Systems', 'Glass Cutting'],
//     manHours: 2050,
//     gender: 'M',
//     rating: 4.5,
//   },
//   {
//     name: 'Michelle Lewis',
//     trade: 'Landscaping',
//     training: 'Landscape Architect',
//     skill: ['Grading', 'Planting', 'Irrigation Systems'],
//     manHours: 1700,
//     gender: 'F',
//     rating: 4.8,
//   },
//   {
//     name: 'Joseph Martin',
//     trade: 'Insulation',
//     training: 'Insulation Specialist',
//     skill: ['Fiberglass', 'Spray Foam', 'Thermal Barriers'],
//     manHours: 1550,
//     gender: 'M',
//     rating: 4.2,
//   },
//   {
//     name: 'Rebecca White',
//     trade: 'Civil Engineering',
//     training: 'Professional Engineer',
//     skill: ['Structural Analysis', 'Site Planning', 'Infrastructure Design'],
//     manHours: 3750,
//     gender: 'F',
//     rating: 4.9,
//   },
// ]

const tableData = ref()
const filters = ref()
// const loading = ref(true)
const selectedItem = ref()
const toast = useToast()
const screenSize = ref(window.innerWidth)

// const store = useLayoutStore()
// const { isBasicSidebarOpen } = storeToRefs(store)

const {
  loading: allWorkerLoading,
  onResult: allWorkerOnResult,
  onError: allWorkerOnError,
} = useGetAllWorkers()

allWorkerOnResult((res) => {
  console.log(res.data)
  tableData.value = convertToWorkerTable(res.data.allWorkers)
})
allWorkerOnError((err) => {
  toast.add({
    severity: 'error',
    summary: 'Error in fetching worker data',
    detail: `${err}`,
    life: 3000,
  })
})

onMounted(() => {
  // tableData.value = workerTableData
  // loading.value = false
  window.addEventListener('resize', () => {
    screenSize.value = window.innerWidth
  })
})

const initFilters = () => {
  filters.value = {
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    nationalId: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    gender: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    phoneNumber: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
  }
}
initFilters()

const clearFilter = () => {
  initFilters()
}
</script>
