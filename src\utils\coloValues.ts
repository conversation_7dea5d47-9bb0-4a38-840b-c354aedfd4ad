export type Shade = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900 | 950
type ColorType = {
  [key: string]: Shade[]
}

export const rootColors: ColorType[] = [
  { emerald: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { green: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { lime: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { red: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { orange: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { amber: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { yellow: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { teal: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { cyan: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { sky: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { blue: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { indigo: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { violet: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { purple: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { fuchsia: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { pink: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { rose: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { slate: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { gray: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { zinc: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { neutral: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
  { stone: [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950] },
]

const shadeValues: Shade[] = rootColors[0].emerald.map((i) => i)
const colors = rootColors.map((i) => Object.keys(i)[0])

export function generateColorValues() {
  // console.log(shadeValues)
  const colorValueIdx = Math.floor(Math.random() * shadeValues.length)
  const colorTypeIdx = Math.floor(Math.random() * rootColors.length)

  const colorValue = { light: 0, dark: 1 }
  if (colorValueIdx == 0) {
    colorValue.light = 0
    colorValue.dark = 1
  } else if (colorValueIdx == shadeValues.length - 1) {
    colorValue.dark = colorValueIdx
    colorValue.light = colorValueIdx - 1
  } else {
    colorValue.light = colorValueIdx
    colorValue.dark = colorValueIdx + 1
  }
  return [colors[colorTypeIdx], shadeValues[colorValue.light], shadeValues[colorValue.dark]]
}
