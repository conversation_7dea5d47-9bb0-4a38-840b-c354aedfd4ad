<style lang="scss" scoped>
.left {
  height: fit-content;
  width: fit-content;
}
</style>
<template>
  <Card class="left">
    <template #header>
      <DatePicker v-model="datePicked" transparent borderless :is-dark="darkModeStatus" />
    </template>
    <template #subtitle>
      <p>{{ datePicked.toDateString() }}</p>
    </template>
  </Card>
</template>
<script setup lang="ts">
import { Card } from 'primevue'
import { DatePicker } from 'v-calendar'
import { ref, watch } from 'vue'
import { useDarkModeStore } from '@/stores/StyleStore'
import { storeToRefs } from 'pinia'

const datePicked = ref<Date>(new Date())

const store = useDarkModeStore()
const { darkModeStatus } = storeToRefs(store)

const emit = defineEmits(['setDate'])

watch(datePicked, () => {
  emit('setDate', datePicked)
})
</script>
