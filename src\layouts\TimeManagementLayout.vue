<style lang="scss" scoped>
.wrapper-1 {
  padding-inline: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  .text {
    text-decoration: none !important;
  }
  .buttons {
    display: flex;
    margin-block: 0.5rem;
    gap: 1rem;
    flex-wrap: wrap;
    .left-btn {
      margin-left: auto;
    }
  }
  .container {
    height: 100%;
  }
}
</style>
<template>
  <div class="wrapper-1">
    <div class="buttons">
      <Button
        v-for="(item, idx) in buttonDetails"
        :key="idx"
        :label="item.label"
        raised
        :pt="{
          label: {
            style: {
              'font-weight': 600,
            },
          },
        }"
        as="router-link"
        :to="{ name: item.to }"
        class="text"
      />
      <Button
        label="Request overtime"
        as="router-link"
        :to="{ name: 'requestOvertime' }"
        severity="success"
        variant="outlined"
        class="left-btn text"
      />
    </div>
    <p v-if="title">{{ title }}</p>
    <div class="container">
      <slot />
    </div>
  </div>
</template>
<script setup lang="ts">
import { Button } from 'primevue'
// const buttonText = ['Attendance', 'Toolbox', 'Overtime Management', 'Time Metrics']
const buttonDetails = [
  {
    label: 'Attendance',
    to: 'attendance',
  },
  {
    label: 'Toolbox',
    to: 'toolbox',
  },
  {
    label: 'Overtime Management',
    to: 'overtime',
  },
  // {
  //   label: 'Time Metrics',
  //   to: 'dashHome',
  // },
]
const { title } = defineProps<{ title?: string }>()
</script>
