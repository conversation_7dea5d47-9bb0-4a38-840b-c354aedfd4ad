import { useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { type TrainingType } from '../graphqlTypes'

export type SimpleTrainingsType = {
  id: string
  name: string
}

export function useGetAllSimpleTrainings() {
  const query = gql`
    query getAllTasks {
      allTrainings {
        id
        name
      }
    }
  `
  return useQuery<{ allTrainings: SimpleTrainingsType[] }>(query)
}

export function useGetAllTrainings() {
  const query = gql`
    query GetAllTrainings {
      allTrainings {
        id
        name
        description
        startDate
        endDate
        duration
        validityPeriodMonths
        trainingType
        trainer
        frequency
        status
        workers {
          id
          name
          company
          trades {
            id
            name
          }
        }
        createdAt
        createdBy
        updatedAt
        updatedBy
      }
    }
  `
  return useQuery<{ allTrainings: TrainingType[] }>(query)
}
