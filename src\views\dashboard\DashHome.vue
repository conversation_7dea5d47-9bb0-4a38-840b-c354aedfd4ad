<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  .part-1 {
    display: flex;
    justify-content: space-between;
    padding-inline: 1rem;
    flex-wrap: wrap;
    gap: 2rem;
    & > * {
      flex: 1;
    }
    .one {
      flex: initial;
    }
  }
  .part-2 {
    display: flex;
    padding-inline: 1rem;
    gap: 2rem;
    .one {
      flex: 2;
    }
    .two {
      flex: 1;
    }
  }
}
</style>

<template>
  <div class="wrapper">
    <div class="part-1">
      <DoughnutChart class="one" />
      <TradesTable class="two" />
      <Equipment class="three" />
    </div>
    <div class="part-2">
      <SiteOverview class="one" />
      <ChecklistTable class="two" />
    </div>
  </div>
</template>
<script setup lang="ts">
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import TradesTable from '@/components/TradesTable.vue'
import Equipment from '@/components/EquipmentTrade.vue'
import SiteOverview from '@/components/SiteOverview.vue'
import ChecklistTable from '@/components/ChecklistTable.vue'
</script>
