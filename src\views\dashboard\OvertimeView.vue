<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 1rem;

  .card {
    width: 100%;
  }
}
</style>

<template>
  <TimeManagementLayout>
    <div class="container">
      <div class="side">
        <DatepickerItem @set-date="() => {}" />
      </div>
      <div class="card">
        <Tabs value="0">
          <TabList>
            <Tab value="0">Overtime Requests</Tab>
            <Tab value="1">History</Tab>
          </TabList>
          <TabPanels>
            <TabPanel value="0">
              <OvertimeApprovalTable />
            </TabPanel>
            <TabPanel value="1">
              <OvertimeTable />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </div>
    </div>
  </TimeManagementLayout>
</template>
<script setup lang="ts">
import Tabs from 'primevue/tabs'
import TabList from 'primevue/tablist'
import Tab from 'primevue/tab'
import TabPanels from 'primevue/tabpanels'
import TabPanel from 'primevue/tabpanel'
import TimeManagementLayout from '@/layouts/TimeManagementLayout.vue'
import OvertimeTable from '@/components/OvertimeTable.vue'
import DatepickerItem from '@/components/DatepickerItem.vue'
import OvertimeApprovalTable from '@/components/OvertimeApprovalTable.vue'
</script>
