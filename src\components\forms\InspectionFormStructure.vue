<style scoped lang="scss">
.approved {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1rem;
  .three {
    width: 50%;
  }
}

.finisher {
  display: flex;
  gap: 2rem;
  .question {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 0.5rem;
  }
  .sign-area {
    flex: 1;
    display: flex;
    width: 100%;
    align-items: center;
    .sign {
      display: flex;
      // justify-content: center;
      align-items: center;
      flex: 1;
    }
    .area {
      flex: 2;
    }
    & > * {
      width: 100%;
      height: 100%;
    }
  }
}
.table-footer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.form-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0 1rem;
}
</style>

<template>
  <div class="form-container">
    <div class="header">
      <h2 style="text-decoration: underline; text-underline-offset: 0.3rem">{{ title }}</h2>
    </div>
    <div class="disclaimer">
      <p>
        <strong><em>Note: </em></strong>Please tick <strong><em>YES</em></strong> or
        <strong><em>NO</em></strong> for all the descriptions as per the condition of the equipment
        onsite
      </p>
    </div>
    <div class="main">
      <div class="column-name">
        <DataTable :value="data" showGridlines>
          <Column field="serialNo" header="S/No"> </Column>
          <Column field="description" header="Description" style="width: fit-content"> </Column>
          <Column header="Answer">
            <template #body="props">
              <FormCheckbox
                :id="`${props.data.serialNo}`"
                @change="(val) => checkBoxHandler(`${props.data.serialNo}`, val)"
              />
            </template>
          </Column>
          <Column header="Remarks" style="width: 50%">
            <template #body="props">
              <Textarea
                style="width: 100%"
                @value-change="(str) => textAreaHandler(`${props.data.serialNo}`, str)"
              />
            </template>
          </Column>
          <template #footer>
            <div class="table-footer">
              <div class="approved">
                <p class="one">Approved</p>
                <p class="two">
                  <FormCheckbox
                    :id="`approved`"
                    @change="(val) => (initialData.approved.value = val)"
                  />
                </p>
                <p class="three"><Textarea style="width: 100%" placeholder="Remarks" rows="5" /></p>
              </div>
              <div>
                <Textarea
                  style="width: 100%"
                  placeholder="General Comments:"
                  rows="5"
                  @value-change="(val) => (initialData.approved.remarks = val)"
                />
              </div>
              <div class="finisher">
                <div class="question">
                  <p>Inspected By:</p>
                  <p>
                    <InputText
                      type="text"
                      style="width: 100%"
                      @value-change="(val) => (initialData.inspectedBy = val ?? '')"
                    />
                  </p>
                  <p>Date:</p>
                  <p>
                    <!-- <InputText type="text" style="width: 100%" /> -->
                    <DatePicker
                      style="width: 100%"
                      @date-select="(dt) => (initialData.date = dt.toISOString())"
                    />
                  </p>
                  <p>Time:</p>
                  <p>
                    <InputText
                      type="text"
                      style="width: 100%"
                      @value-change="(tm) => (initialData.time = tm ?? '')"
                    />
                  </p>
                </div>
                <div class="sign-area">
                  <div class="sign">
                    <p>Signature</p>
                  </div>
                  <div class="area">
                    <Textarea
                      style="width: 100%; height: 100%"
                      @value-change="(val) => (initialData.signature = val)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </DataTable>
        <Button
          @click="clickHandler"
          label="submit"
          size="large"
          style="margin: 1rem"
          type="submit"
          variant="outlined"
          severity="success"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { DataTable, Column, Button } from 'primevue'
import { onMounted, reactive, ref } from 'vue'
import FormCheckbox from './FormCheckbox.vue'
import Textarea from 'primevue/textarea'
import InputText from 'primevue/inputtext'
import { type FormStructureType } from '@/utils/formCreatingData'
import DatePicker from 'primevue/datepicker'

type OutputData = {
  id: string
  answers: {
    serialNo: string
    value: boolean
    remarks: string
  }[]
  approved: {
    value: boolean
    remarks: string
  }
  comments: string
  inspectedBy: string
  date: string
  time: string
  signature: string
}

const {
  title = '',
  inspectionItems = [],
  id,
} = defineProps<{
  title: string
  inspectionItems: FormStructureType[]
  id: string
}>()

// const inspectionItems: FormStructureType[] = [
//   { serialNo: 1, description: 'Machine in good working condition', response: "", remarks: '' },
//   { serialNo: 2, description: 'On/off switch operational', response: "", remarks: '' },
//   { serialNo: 3, description: 'Handles in good condition', response: "", remarks: '' },
//   {
//     serialNo: 4,
//     description: 'Is the machine anchored and positioned on a flat, stable surface?',
//     response: "",
//     remarks: '',
//   },
//   { serialNo: 5, description: 'Machine double insulated or grounded', response: "", remarks: '' },
//   {
//     serialNo: 6,
//     description: 'Always use designated key for changing the cutting discs',
//     response: "",
//     remarks: '',
//   },
//   { serialNo: 7, description: 'Machine operated by competent person', response: "", remarks: '' },
//   {
//     serialNo: 8,
//     description: 'Is machine unplugged from the power source when not in use?',
//     response: "",
//     remarks: '',
//   },
//   {
//     serialNo: 9,
//     description:
//       'Operator provided with PPE (clear impact resistant glasses, steel toe safety shoes, cut resistant gloves, dust masks)',
//     response: "",
//     remarks: '',
//   },
//   {
//     serialNo: 10,
//     description: 'Guard fixed to cover the rotating parts',
//     response: "",
//     remarks: '',
//   },
//   { serialNo: 11, description: 'Auto adjustable guard functional', response: "", remarks: '' },
//   {
//     serialNo: 12,
//     description:
//       'Cutting discs free from defects and rotating capacity has been marked. Standard quality of cutting disc used',
//     response: "",
//     remarks: '',
//   },
//   { serialNo: 13, description: 'Positioning screw functional', response: "", remarks: '' },
//   {
//     serialNo: 14,
//     description: 'Power cable free from damage and connection taken through industrial plug',
//     response: "",
//     remarks: '',
//   },
// ]

const data = ref()

const setupOutput = (): OutputData => {
  return {
    id: id,
    answers: inspectionItems.map((i) => ({
      serialNo: `${i.serialNo}`,
      value: false,
      remarks: '',
    })),
    approved: {
      value: false,
      remarks: '',
    },
    comments: '',
    inspectedBy: '',
    date: '',
    time: '',
    signature: '',
  }
}

const initialData = reactive(setupOutput())

const textAreaHandler = (sNo: string, val: string): void => {
  const i = initialData.answers.find((t) => t.serialNo === sNo)
  if (!i) return
  i.remarks = val
}
const checkBoxHandler = (sNo: string, val: boolean): void => {
  const i = initialData.answers.find((t) => t.serialNo === sNo)
  if (!i) return
  i.value = val
}
const clickHandler = () => {
  console.log(initialData)
}
onMounted(() => {
  data.value = inspectionItems
})
</script>
