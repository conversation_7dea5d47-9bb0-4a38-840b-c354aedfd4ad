import attendeeData from './mockData/attendeeData.json'

export interface TimeDelta {
  days: number
  hours: number
  minutes: number
  seconds: number
  milliseconds: number
}

export function getTimeDifference(start: Date, end: Date): TimeDelta {
  const diff = Math.abs(end.getTime() - start.getTime())

  return {
    days: Math.floor(diff / (1000 * 60 * 60 * 24)),
    hours: Math.floor((diff / (1000 * 60 * 60)) % 24),
    minutes: Math.floor((diff / (1000 * 60)) % 60),
    seconds: Math.floor((diff / 1000) % 60),
    milliseconds: diff % 1000,
  }
}

export function getWorkHours(start?: Date, end?: Date) {
  if (!start || !end) {
    return null
  }
  return getTimeDifference(start, end)
}

export interface AttendeeType {
  fullName: string
  requiredTime: number
  clockIn?: Date
  clockOut?: Date
  breakTime?: Date
  overtime?: Date
  timeOff?: Date
}

export interface ExtendedAttendeeType extends AttendeeType {
  workTime?: TimeDelta
}

export const getAttendeeData = (): AttendeeType[] => {
  const data = attendeeData.map((attendee) => ({
    fullName: attendee['fullName'],
    requiredTime: 8,
    clockIn: attendee['clockIn'] ? new Date(attendee.clockIn) : undefined,
    clockOut: attendee.clockOut ? new Date(attendee.clockOut) : undefined,
    breakTime: attendee.breakTime ? new Date(attendee.breakTime) : undefined,
    overtime: attendee.overtime ? new Date(attendee.overtime) : undefined,
    timeOff: attendee.timeOff ? new Date(attendee.timeOff) : undefined,
  }))
  return data
}

export const getExtendedAttendeeData = (): ExtendedAttendeeType[] => {
  const data = getAttendeeData().map((i) => ({
    ...i,
    workTime: getWorkHours(i.clockIn, i.clockOut) ?? undefined,
  }))
  return data
}
