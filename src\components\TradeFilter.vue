<template>
  <div class="trade-filter">
    <div class="filter-header">
      <label for="trade-filter">Filter by Trade Group:</label>
    </div>
    <div class="filter-controls">
      <Dropdown
        id="trade-filter"
        v-model="selectedTrade"
        :options="tradeOptions"
        optionLabel="label"
        optionValue="value"
        placeholder="All Trades"
        class="trade-dropdown"
        @change="handleTradeChange"
      />
      <Button
        v-if="selectedTrade"
        icon="pi pi-times"
        severity="secondary"
        text
        @click="clearFilter"
        class="clear-button"
        v-tooltip="'Clear filter'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Dropdown from 'primevue/dropdown'
import Button from 'primevue/button'
import { useGetAllTrades } from '@/graphQL/Queries/useGetAllTrades'
import type { TradeType } from '@/graphQL/graphqlTypes'

interface Emits {
  (e: 'trade-selected', tradeId: number | null): void
}

const emit = defineEmits<Emits>()

const { result: tradesResult, loading: tradesLoading } = useGetAllTrades()
const selectedTrade = ref<number | null>(null)

const tradeOptions = computed(() => {
  if (!tradesResult.value?.allTrades) return []
  
  return tradesResult.value.allTrades.map((trade: TradeType) => ({
    label: trade.name,
    value: trade.id
  }))
})

const handleTradeChange = () => {
  emit('trade-selected', selectedTrade.value)
}

const clearFilter = () => {
  selectedTrade.value = null
  emit('trade-selected', null)
}

// Watch for changes in trades data to ensure reactivity
watch(tradesResult, () => {
  // If the selected trade is no longer available, clear the selection
  if (selectedTrade.value && tradesResult.value?.allTrades) {
    const tradeExists = tradesResult.value.allTrades.some(
      (trade: TradeType) => trade.id === selectedTrade.value
    )
    if (!tradeExists) {
      clearFilter()
    }
  }
})
</script>

<style scoped lang="scss">
.trade-filter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;

  .filter-header {
    label {
      font-weight: 600;
      color: var(--text-color);
      font-size: 0.875rem;
    }
  }

  .filter-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .trade-dropdown {
      min-width: 200px;
    }

    .clear-button {
      padding: 0.25rem;
      width: 2rem;
      height: 2rem;
    }
  }
}

@media (min-width: 768px) {
  .trade-filter {
    flex-direction: row;
    align-items: center;
    gap: 1rem;

    .filter-header {
      flex-shrink: 0;
    }

    .filter-controls {
      flex: 1;
    }
  }
}
</style>
