<style lang="scss" scoped>
.country-view {
  width: 100%;
  display: flex;
  gap: 1rem;
  padding: 1rem;
  flex-wrap: wrap;
  .dark-bg {
    box-shadow: 0px 0px 20px 2px var(--p-emerald-600);
  }
  .light-bg {
    //   box-shadow: 0px 0px 20px 2px var(--p-emerald-400);
    // box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }
  .site-number {
    // border: 1px solid red;
    padding-inline: 1rem;
    padding-block: 0.25rem;
    // font-size: 1.2rem;
    font-weight: bolder;
    border-radius: 5px;
    // background-color: var(--p-primary-color);
    background-color: var(--p-button-primary-background);
    color: var(--p-button-primary-color);
  }
  .footer {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
  .card-title {
    font-weight: bolder;
  }
  .footer-text {
    font-weight: bold;
    font-size: 1.2rem;
  }
  .country-item {
    min-width: fit-content;
    flex-grow: 1;
    background-position: center;
    background-size: cover;
    &:hover {
      cursor: pointer;
      box-shadow: 0px 0px 10px 1px var(--p-emerald-600);
    }
  }
}
.table-wrapper {
  padding: 1rem;
  width: 100%;
  .table-top {
    margin-block: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .table-title {
    font-weight: bolder;
    font-size: 1.5rem;
    text-decoration: underline;
    text-underline-offset: 8px;
    text-decoration-thickness: 4px;
    color: var(--p-primary-color);
  }
}
</style>

<template>
  <div style="height: 100%">
    <div style="display: flex; padding-inline: 1rem; margin-block: 1rem">
      <Button style="margin-left: auto" label="Add site" icon="pi pi-plus" />
    </div>
    <div class="country-view">
      <Card
        v-for="(data, i) in countries"
        :key="i"
        class="country-item"
        @click="setTableData(data)"
      >
        <template #title>
          <p class="card-title">
            {{ data.name }}
          </p>
        </template>
        <template #footer>
          <div class="footer">
            <span
              class="site-number"
              :class="{ 'dark-bg': darkModeStatus, 'light-bg': !darkModeStatus }"
            >
              {{ data.sites.length }}
            </span>
            <span class="footer-text">Sites</span>
          </div>
        </template>
      </Card>
    </div>
    <hr />
    <div class="table-wrapper">
      <div class="table-top">
        <h3 class="table-title">
          <span>{{ tableData.name }}</span
          >'s Sites
        </h3>
      </div>
      <DataTable :value="tableData.sites" size="large" showGridlines stripedRows>
        <Column field="name" sortable style="" header="Name"></Column>
        <Column field="startDate" sortable style="" header="Starting Date"></Column>
      </DataTable>
    </div>
    <!-- This is to add button colors to :root so that i could use its colors
        Its not intended for DOM but remove if you are no longer using its colors
    -->
    <Button v-show="false" />
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { Card, Button } from 'primevue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { useDarkModeStore } from '@/stores/StyleStore'

type SiteType = {
  name: string
  startDate: Date | string
}
type CountryDataType = {
  name: string
  sites: SiteType[]
}

const sites: SiteType[] = [
  { name: 'Safaricom', startDate: new Date().toDateString() },
  { name: 'Hewatele', startDate: new Date().toDateString() },
  { name: 'ENI', startDate: new Date().toDateString() },
  { name: 'Jogoo', startDate: new Date().toDateString() },
]
const countries: CountryDataType[] = [
  {
    name: 'Kenya',
    sites: sites,
  },
  {
    name: 'Ethiopia',
    sites: sites,
  },
  {
    name: 'Uganda',
    sites: sites,
  },
  {
    name: 'Tanzania',
    sites: sites,
  },
]
const tableData = ref<CountryDataType>(countries[0])
function setTableData(data: CountryDataType) {
  tableData.value = data
}

const DarkModeStore = useDarkModeStore()
const { darkModeStatus } = storeToRefs(DarkModeStore)
</script>
