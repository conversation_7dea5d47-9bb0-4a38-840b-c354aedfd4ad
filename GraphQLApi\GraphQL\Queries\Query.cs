﻿using GraphQLApi.Services;
using Shared.GraphQL.Models;
using Task = Shared.GraphQL.Models.Task;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;

        public Query(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            ITaskService taskService,
            IEquipmentService equipmentService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            _taskService = taskService;
            _equipmentService = equipmentService;
        }

        public async Task<IEnumerable<Worker>> GetAllWorkers()
        {
            return await _workerService.GetAllWorkersAsync();
        }

        public async Task<Worker?> GetWorkerById(int id)
        {
            return await _workerService.GetWorkerByIdAsync(id);
        }

        public async Task<IEnumerable<Training>> GetAllTrainings()
        {
            return await _trainingService.GetAllTrainingsAsync();
        }

        public async Task<Training?> GetTrainingById(int id)
        {
            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<IEnumerable<Trade>> GetAllTrades()
        {
            return await _tradeService.GetAllTradesAsync();
        }

        public async Task<Trade?> GetTradeById(int id)
        {
            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<IEnumerable<Skill>> GetAllSkills()
        {
            return await _skillService.GetAllSkillsAsync();
        }

        public async Task<Skill?> GetSkillById(int id)
        {
            return await _skillService.GetSkillByIdAsync(id);
        }

        // Training History Queries
        public async Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistory(int workerId)
        {
            return await _trainingStatusService.GetWorkerTrainingHistoryAsync(workerId);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainings(int daysAhead = 30)
        {
            return await _trainingStatusService.GetExpiringTrainingsAsync(daysAhead);
        }

        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainings()
        {
            return await _trainingStatusService.GetExpiredTrainingsAsync();
        }

        // Task Queries
        public async Task<IEnumerable<Task>> GetAllTasks()
        {
            return await _taskService.GetAllTasksAsync();
        }

        public async Task<Task?> GetTaskById(int id)
        {
            return await _taskService.GetTaskByIdAsync(id);
        }

        public async Task<IEnumerable<Task>> GetTasksByWorkerId(int workerId)
        {
            return await _taskService.GetTasksByWorkerIdAsync(workerId);
        }

        public async Task<IEnumerable<Task>> GetTasksByChiefEngineerId(int chiefEngineerId)
        {
            return await _taskService.GetTasksByChiefEngineerIdAsync(chiefEngineerId);
        }

        public async Task<IEnumerable<Task>> GetTasksByStatus(Shared.Enums.TaskStatus status)
        {
            return await _taskService.GetTasksByStatusAsync(status);
        }

        public async Task<IEnumerable<Task>> GetTasksByPriority(Shared.Enums.TaskPriority priority)
        {
            return await _taskService.GetTasksByPriorityAsync(priority);
        }

        // Equipment Queries
        public async Task<IEnumerable<Equipment>> GetAllEquipment()
        {
            return await _equipmentService.GetAllEquipmentAsync();
        }

        public async Task<Equipment?> GetEquipmentById(int id)
        {
            return await _equipmentService.GetEquipmentByIdAsync(id);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByStatus(string status)
        {
            return await _equipmentService.GetEquipmentByStatusAsync(status);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByCategory(string category)
        {
            return await _equipmentService.GetEquipmentByCategoryAsync(category);
        }

        public async Task<IEnumerable<Equipment>> GetEquipmentByLocation(string location)
        {
            return await _equipmentService.GetEquipmentByLocationAsync(location);
        }
    }
}
