{"name": "workforce_frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@apollo/client": "^3.13.5", "@popperjs/core": "^2.11.8", "@primeuix/styles": "^1.0.0", "@primevue/forms": "^4.3.1", "@primevue/themes": "^4.2.5", "@vue/apollo-composable": "^4.2.2", "chart.js": "^4.4.7", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "pinia": "^2.3.1", "primeicons": "^7.0.0", "primevue": "^4.2.5", "v-calendar": "^3.1.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.7", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/eslint-plugin": "1.1.25", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cypress": "^14.0.0", "eslint": "^9.18.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "start-server-and-test": "^2.0.10", "typescript": "~5.7.3", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0", "vitest": "^3.0.2", "vue-tsc": "^2.2.0"}}