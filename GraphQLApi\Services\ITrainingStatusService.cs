using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface ITrainingStatusService
    {
        System.Threading.Tasks.Task UpdateTrainingStatusesAsync();
        System.Threading.Tasks.Task UpdateTrainingHistoryStatusesAsync();
        System.Threading.Tasks.Task<WorkerTrainingHistory> CompleteTrainingAsync(int workerId, int trainingId, DateTime completionDate, double? score = null, string? notes = null);
        System.Threading.Tasks.Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistoryAsync(int workerId);
        System.Threading.Tasks.Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainingsAsync(int daysAhead = 30);
        System.Threading.Tasks.Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainingsAsync();
    }
}
