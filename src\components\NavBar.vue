<template>
  <div class="card">
    <Menubar>
      <template #start>
        <!-- <i class="pi pi-bars" style="margin-right: 1rem;"></i> -->
        <MenuFill class="menu-icon" @click="handleMenuClick" />
        <h2 class="p-0 text-sm">{{ title }}</h2>
      </template>

      <template #end>
        <div class="flex items-center gap-2">
          <!-- <InputText placeholder="Search" type= "text" class="w-32 sm:w-auto" /> -->
          <i
            class="pi pi-sun pointer"
            @click="DarkModeStore.toggleDarkMode"
            v-show="darkModeStatus"
            style="font-size: 1.5rem; margin-inline: 0.5rem"
          ></i>
          <i
            class="pi pi-moon pointer"
            @click="DarkModeStore.toggleDarkMode"
            v-show="!darkModeStatus"
            style="font-size: 1.5rem; margin-inline: 0.5rem"
          ></i>

          <i class="pi pi-bell" style="font-size: 1.5rem; margin-inline: 0.5rem"></i>
          <i class="pi pi-cog" style="font-size: 1.5rem; margin-inline: 0.5rem"></i>
          <Avatar
            image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png"
            shape="circle"
          />
        </div>
      </template>
    </Menubar>
  </div>
</template>
<style scoped lang="scss">
.menu-icon {
  margin-right: 1rem;
  cursor: pointer;
  border-radius: 50%;
}
.pointer:hover {
  cursor: pointer;
}
.p-menubar {
  border-color: transparent;
  background-color: initial;
}
</style>
<script setup lang="ts">
import Menubar from 'primevue/menubar'
import { Avatar } from 'primevue'
// import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import MenuFill from '@/icons/MenuFill.vue'
import { useLayoutStore } from '@/stores/LayoutStore'
import { useDarkModeStore } from '@/stores/StyleStore'

const DarkModeStore = useDarkModeStore()
const LayoutStore = useLayoutStore()

function handleMenuClick() {
  LayoutStore.toggleBasicSidebar()
}

const { title = '' } = defineProps<{
  title?: string
}>()
const { darkModeStatus } = storeToRefs(DarkModeStore)
</script>
