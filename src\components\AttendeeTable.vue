<style lang="scss" scoped>
.table-cell {
  display: flex;
  align-items: center;
}
</style>
<template>
  <div class="card">
    <Card>
      <template #content>
        <DataTable
          stripedRows
          showGridlines
          v-model:filters="filters"
          :value="attendees"
          paginator
          :rows="10"
          dataKey="id"
          filterDisplay="row"
          :loading="loading"
          :globalFilterFields="['name', 'clockIn', 'clockOut']"
          :size="isBasicSidebarOpen || screenSize < 1300 ? 'small' : undefined"
        >
          <template #header>
            <div class="flex justify-end">
              <IconField>
                <InputIcon>
                  <i class="pi pi-search" />
                </InputIcon>
                <InputText v-model="filters['global'].value" placeholder="Keyword Search" />
              </IconField>
            </div>
          </template>

          <template #empty> No worker found. </template>

          <template #loading> Loading worker data. Please wait. </template>

          <Column field="fullName" header="Name" style="">
            <template #body="{ data }">
              {{ data.fullName }}
            </template>

            <template #filter="{ filterModel, filterCallback }">
              <InputText
                v-model="filterModel.value"
                type="text"
                @input="filterCallback()"
                placeholder="Search by name"
              />
            </template>
          </Column>

          <Column header="Clock In" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{ data.clockIn ? data.clockIn.toLocaleTimeString() : '-' }}</span>
              </div>
            </template>
          </Column>
          <Column header="Clock Out" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{ data.clockOut ? data.clockOut.toLocaleTimeString() : '-' }}</span>
              </div>
            </template>
          </Column>
          <Column header="Work Hours" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{
                  data.workTime ? `${data.workTime.hours}h ${data.workTime.minutes}m` : '-'
                }}</span>
              </div>
            </template>
          </Column>
          <Column header="Breaktime" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{ data.breakTime ? data.breakTime.toLocaleTimeString() : '-' }}</span>
              </div>
            </template>
          </Column>
          <Column header="Required Time" style="">
            <template #body="{ data }">
              <div class="table-cell" style="justify-content: center">
                <span>{{ `${data.requiredTime}h` }}</span>
              </div>
            </template>
          </Column>
          <Column header="Time Off" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{ data.timeOff ? data.timeOff.toLocaleTimeString() : '-' }}</span>
              </div>
            </template>
          </Column>
          <Column header="Overtime" style="">
            <template #body="{ data }">
              <div class="table-cell">
                <span>{{ data.overtime ? data.overtime.toLocaleTimeString() : '-' }}</span>
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>
  </div>
</template>
<script setup lang="ts">
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { IconField, InputIcon, InputText, Card } from 'primevue'
import { ref, onMounted } from 'vue'
import { FilterMatchMode } from '@primevue/core/api'
import type { ExtendedAttendeeType } from '@/utils/attendeeMockData'
import { getExtendedAttendeeData } from '@/utils/attendeeMockData'
import { useLayoutStore } from '@/stores/LayoutStore'
import { storeToRefs } from 'pinia'

const attendees = ref<ExtendedAttendeeType[]>()
const loading = ref(true)
const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS },
  fullName: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
})
const screenSize = ref(window.innerWidth)

const store = useLayoutStore()
const { isBasicSidebarOpen } = storeToRefs(store)

onMounted(() => {
  attendees.value = getExtendedAttendeeData()
  loading.value = false
  window.addEventListener('resize', () => {
    screenSize.value = window.innerWidth
  })
})
</script>
