import { useQuery } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { type WorkerType } from '../graphqlTypes'
import type { UpdateWorkerParameterType } from '../Mutations/useUpdateWorker'

export type WorkerByIdType = {
  id: number
  name: string
  company: string
  trade: string
  // skill: string
  skills: string[]
  nationalId: string
  gender: string
  age: number
  // trainingsCompleted: number
  trainingIds: number[]
  manHours: number
  rating: number
  createdBy: string
  createdAt: string // DateTime is usually represented as a string in GraphQL responses
  photoUrl?: string | null
  phoneNumber: string
  email?: string | null
  inductionDate?: string | null
  medicalCheckDate?: string | null
  updatedAt?: string | null
  updatedBy?: string | null
}

export function toUpdateWorkerParameter(data: WorkerByIdType): UpdateWorkerParameterType {
  return {
    id: data.id,
    name: data.name,
    company: data.company,
    trade: data.trade,
    skills: data.skills,
    trainingIds: data.trainingIds, // Note: spelling corrected below
    manHours: data.manHours,
    rating: data.rating,
    gender: data.gender,
    phoneNumber: data.phoneNumber,
    email: data.email ?? undefined,
    inductionDate: data.inductionDate ? new Date(data.inductionDate) : undefined,
    medicalCheckDate: data.medicalCheckDate ? new Date(data.medicalCheckDate) : undefined,
  }
}

export function useWorkerById(id: number) {
  const workerByIdQL = gql`
    query getWorkerById($id: Int!) {
      workerById(id: $id) {
        id
        name
        nationalId
        phoneNumber
        email
        age
        company
        trades {
          id
          name
        }
        skills {
          id
          name
        }
        trainings {
          id
          name
        }
        gender
        rating
        createdBy
        createdAt
        photoUrl
        inductionDate
        medicalCheckDate
        updatedAt
        updatedBy
      }
    }
  `
  return useQuery<{ workerById: WorkerType }>(workerByIdQL, { id })
}
