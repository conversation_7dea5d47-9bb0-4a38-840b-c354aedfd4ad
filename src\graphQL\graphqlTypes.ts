export type WorkerType = {
  id: number
  name: string
  company: string
  nationalId: string
  gender: string
  dateOfBirth: string | null // ISO date
  inductionDate: string | null // ISO datetime
  medicalCheckDate: string | null
  photoUrl: string | null
  phoneNumber: string | null
  email: string | null
  age: number | null
  trainingsCompleted: number
  manHours: number
  rating: number
  trainings: TrainingType[] | null
  trades: TradeType[] | null
  skills: SkillType[] | null
  createdAt: string
  createdBy: string
  updatedAt: string | null
  updatedBy: string
}

export type SkillType = {
  id: number
  name: string
  description: string | null
  // workers: WorkerSummary[] | null;
  createdAt: string
  createdBy: string
  updatedAt: string | null
  updatedBy: string
}

export type TrainingType = {
  id: number
  name: string
  description: string | null
  startDate: string | null // ISO datetime
  endDate: string | null // ISO datetime
  duration: string | null
  validityPeriodMonths: number | null
  trainingType: string | null
  trainer: string | null
  frequency: string | null
  status: TrainingStatus
  workers: WorkerType[] | null
  trainingHistory: WorkerTrainingHistoryType[] | null
  createdAt: string
  createdBy: string
  updatedAt: string | null
  updatedBy: string
}

export type WorkerTrainingHistoryType = {
  id: number
  workerId: number
  trainingId: number
  worker: WorkerType
  training: TrainingType
  completionDate: string // ISO datetime
  expiryDate: string | null // ISO datetime
  status: TrainingStatus
  notes: string | null
  certificateUrl: string | null
  score: number | null
  createdAt: string
  createdBy: string
  updatedAt: string | null
  updatedBy: string
}

export type TradeType = {
  id: number
  name: string
  description: string | null
  workers: WorkerType[] | null
  createdAt: string
  createdBy: string
  updatedAt: string | null
  updatedBy: string
}

export enum TrainingStatus {
  Scheduled = 'SCHEDULED',
  Active = 'ACTIVE',
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  Cancelled = 'CANCELLED'
}


