<template>
  <div>
    <div v-if="!item">
      <h2>Form with id of {{ id }} not found</h2>
    </div>
    <div v-else>
      <InspectionFormStructure
        :id="item.id"
        :title="`${item.name} inspection form`.toUpperCase()"
        :inspectionItems="item.information"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import InspectionFormStructure from '@/components/forms/InspectionFormStructure.vue'
import { useRoute } from 'vue-router'
import { inspectionFormTypes } from '@/utils/formCreatingData'

const route = useRoute()
const id = route.params.id
const item = inspectionFormTypes.find((item) => item.id === id)
</script>
