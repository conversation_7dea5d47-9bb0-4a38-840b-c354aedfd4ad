<style lang="scss" scoped>
.profile-wrapper {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;

  & > * {
    flex: 1;
    // border-top: solid var(--p-text-color) 2px;
    padding: 0rem 1rem;
    display: flex;
    flex-direction: column;

    img {
      width: 100%;
      border-radius: 8px;
      flex-shrink: 0;
    }
    .no-picture {
      min-height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .generic-item {
      display: flex;
      gap: 0.5rem;
      padding: 0.5rem 0rem;
      margin: 1rem 0rem;

      // border: 1px solid indigo;

      & > * {
        width: 100%;
      }

      > :first-child {
        font-weight: bold;
        font-size: 1.1rem;
      }
    }

    .age-gender {
      display: flex;
      justify-content: space-between;
      // border: 1px solid indigo;
      padding: 0.5rem 0.5rem;
      margin: 1rem 0rem;

      & > * {
        > :first-child {
          font-weight: bold;
          font-size: 1.1rem;
        }
      }
    }

    .list-item {
      display: flex;
      // border: 1px solid indigo;
      width: 100%;
      justify-content: space-between;
      // flex-direction: column;
      gap: 0.25rem;
      margin: 0.5rem 0rem;
      // padding-right: 1rem;

      span {
        font-weight: bold;
        font-size: 1.1rem;
        width: 100%;
      }

      ul {
        width: 100%;
        // border: 1px solid indigo;
        padding-block: 0.5rem;
        padding-inline: 0.25rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      li {
        // border: 1px solid indigo;
        list-style: none;
      }
    }

    .date-cell {
      font-size: 0.9rem;
      display: flex;
      align-items: center;
    }
  }

  .one {
    border-right: solid var(--p-text-color) 2px;

    .generic-item {
      border-bottom: solid var(--p-text-color) 2px;
    }
  }

  .two {
    // justify-content: space-between;
    .generic-item {
      margin-top: 0%;
      margin-bottom: 1rem;
    }
  }

  .three {
    .generic-item {
      margin-top: 0%;
      margin-bottom: 1rem;

      & > * {
        width: 100%;
      }
    }
  }

  .btns {
    display: flex;
    justify-content: space-between;
  }
}
</style>
<template>
  <div v-if="workerLoading">
    <ProgressSpinner />
  </div>
  <div class="profile-wrapper" v-else-if="workerData">
    <div class="one">
      <!-- <div class="img-wrapper"> -->
      <img
        v-if="workerData.photoUrl"
        :src="workerData.photoUrl"
        :alt="`${workerData.name} profile picture`"
      />
      <p v-else class="no-picture">No picture given</p>
      <!-- </div> -->
      <p class="generic-item">
        <span>Name :</span>
        <span>{{ workerData.name }}</span>
      </p>
      <p class="generic-item">
        <span>Id number :</span><span>{{ workerData.nationalId }}</span>
      </p>
      <p class="generic-item">
        <span>Phone Number :</span><span>{{ workerData.phoneNumber }}</span>
      </p>
      <p class="generic-item">
        <span>Email :</span><span>{{ workerData.email ?? 'no email given' }}</span>
      </p>
      <p class="age-gender">
        <span>
          <span>Age : </span>
          <span>{{ `${workerData.age}` }}</span>
        </span>
        <span>
          <span>Gender : </span>
          <span>{{ `${workerData.gender}` }}</span>
        </span>
      </p>
    </div>
    <div class="two">
      <p class="generic-item">
        <span>Company</span><span>{{ workerData.company }}</span>
      </p>
      <p class="generic-item">
        <span>Rating : </span><span>{{ workerData.rating }}</span>
      </p>
      <p class="generic-item">
        <span>Created By : </span><span>{{ workerData.createdBy }}</span>
      </p>
      <p class="generic-item">
        <span>Created At : </span
        ><span class="date-cell">{{ new Date(workerData.createdAt).toLocaleString() }}</span>
      </p>
      <p class="generic-item">
        <span>Induction Date : </span
        ><span class="date-cell">{{
          workerData.inductionDate
            ? new Date(workerData.inductionDate).toLocaleString()
            : 'Date not given'
        }}</span>
      </p>
      <p class="generic-item">
        <span>Medical Date : </span
        ><span class="date-cell">{{
          workerData.medicalCheckDate
            ? new Date(workerData.medicalCheckDate).toLocaleString()
            : 'Date not given.'
        }}</span>
      </p>
      <p class="generic-item">
        <span>Last Updated By : </span><span>{{ workerData.updatedBy }}</span>
      </p>
      <p class="generic-item">
        <span>Last Update Date : </span
        ><span class="date-cell">{{
          workerData.updatedAt ? new Date(workerData.updatedAt).toLocaleString() : 'Date not given'
        }}</span>
      </p>
    </div>
    <div class="three">
      <div class="list-item">
        <span>Skills</span>
        <ul>
          <template v-if="workerData.skills?.length">
            <li v-for="item in workerData.skills" :key="item.id">{{ item.name }}</li>
          </template>
          <template v-else>
            <li>No skills found</li>
          </template>
        </ul>
      </div>

      <div class="list-item">
        <span>Trade</span>
        <ul>
          <template v-if="workerData.trades?.length">
            <li v-for="item in workerData.trades" :key="item.id">{{ item.name }}</li>
          </template>
          <template v-else>
            <li>No trades found</li>
          </template>
        </ul>
      </div>

      <div class="list-item">
        <span>Trainings</span>
        <ul>
          <template v-if="workerData.trainings?.length">
            <li v-for="item in workerData.trainings" :key="item.id">{{ item.name }}</li>
          </template>
          <template v-else>
            <li>No trainings found</li>
          </template>
        </ul>
      </div>
      <p class="btns">
        <Button
          severity="success"
          label="Edit Profile"
          raised
          @click="(_) => handleEditBtnClick()"
        />
        <Button severity="danger" label="Delete Profile" raised />
      </p>
    </div>
  </div>
  <div v-else>
    <h3>User NotFound</h3>
  </div>
</template>
<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { Button } from 'primevue'
import { useWorkerById } from '@/graphQL/Queries/useWorkerId'
import { ref } from 'vue'
import type { WorkerType } from '@/graphQL/graphqlTypes'
import { useToast } from 'primevue'
import ProgressSpinner from 'primevue/progressspinner'

const toast = useToast()
const route = useRoute()
const router = useRouter()
const id = route.params.id as string
const workerData = ref<WorkerType | null>(null)

const {
  onResult: workerOnResult,
  onError: workerOnError,
  loading: workerLoading,
} = useWorkerById(parseInt(id))

workerOnResult((res) => {
  // console.log(res.data)
  if (res.data && res.data.workerById) {
    workerData.value = res.data.workerById
  }
})

workerOnError((err) => {
  toast.add({
    severity: 'error',
    summary: 'Error in loading worker profile',
    detail: err.message,
    life: 3000,
  })
})

function handleEditBtnClick() {
  router.push({ name: 'updateWorker', params: { id } })
}

// const workerDummyData = {
//   id: id,
//   name: 'John Doe', //done
//   nationalId: 'A123456789', // done
//   phoneNumber: '+1234567890', // done
//   email: '<EMAIL>', // done
//   age: 35, // done
//   company: 'Acme Construction Ltd.', // done
//   trade: ['Electrician', 'Welder'], // done
//   skills: ['Wiring', 'Soldering', 'Blueprint Reading'], // done
//   trainings: ['Fitting', 'Safety', 'Management'], // done
//   gender: 'Male', // done
//   // manHours: 1200,
//   rating: 4.5,
//   createdBy: 'admin', // done
//   createdAt: '2025-01-15T10:30:00Z', // done
//   photoUrl: 'https://cdn.pixabay.com/photo/2022/12/24/21/14/portrait-7676482_1280.jpg', //done
//   inductionDate: '2025-01-20T08:00:00Z', // done
//   medicalCheckDate: '2025-01-10T09:00:00Z', // done
//   updatedAt: '2025-05-01T14:45:00Z', // done
//   updatedBy: 'supervisor1', // done
// }
</script>
