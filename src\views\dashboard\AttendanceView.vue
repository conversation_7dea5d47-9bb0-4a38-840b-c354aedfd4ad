<style lang="scss" scoped>
.wrapper-tm {
  display: flex;
  height: 100%;
  @media only screen and (max-width: 64rem) {
    flex-wrap: wrap;
  }
  .left {
    height: fit-content;
    width: fit-content;
  }
  .right {
    width: fit-content;
    height: 100%;
    padding: 0rem 1rem;
  }
}
</style>
<template>
  <TimeManagementLayout>
    <div class="wrapper-tm" :style="{ flexWrap: isBasicSidebarOpen ? 'wrap' : 'nowrap' }">
      <Card class="left">
        <template #header>
          <DatePicker v-model="datePicked" transparent borderless :is-dark="darkModeStatus" />
        </template>
        <template #subtitle>
          <p>{{ datePicked.toDateString() }}</p>
        </template>
      </Card>
      <div class="right">
        <AttendeeTable />
      </div>
    </div>
  </TimeManagementLayout>
</template>
<script setup lang="ts">
import { DatePicker } from 'v-calendar'
import { Card } from 'primevue'
import TimeManagementLayout from '@/layouts/TimeManagementLayout.vue'
import { useDarkModeStore } from '@/stores/StyleStore'
import { useLayoutStore } from '@/stores/LayoutStore'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import AttendeeTable from '@/components/AttendeeTable.vue'

const datePicked = ref<Date>(new Date())

const store = useDarkModeStore()
const { darkModeStatus } = storeToRefs(store)

const layoutStore = useLayoutStore()
const { isBasicSidebarOpen } = storeToRefs(layoutStore)
</script>
