import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useDarkModeStore = defineStore('darkMode', () => {
  const darkModeStatus = ref(document.documentElement.classList.contains('my-app-dark'))

  function toggleDarkMode() {
    document.documentElement.classList.toggle('my-app-dark')
    darkModeStatus.value = document.documentElement.classList.contains('my-app-dark')
  }
  return { darkModeStatus, toggleDarkMode }
})
