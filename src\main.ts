import './assets/main.css'
import { ToastService } from 'primevue'
import { DefaultApolloClient } from '@vue/apollo-composable'
import { InMemoryCache, ApolloClient } from '@apollo/client/core'
import { provide, h } from 'vue'
import PrimeVue from 'primevue/config'
import Aura from '@primevue/themes/aura'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

const cache = new InMemoryCache()
const apolloClient = new ApolloClient({
  cache,
  uri: import.meta.env.VITE_GRAPHQL_URI_1,
})

const app = createApp({
  setup() {
    provide(DefaultApolloClient, apolloClient)
  },
  render: () => h(App),
})

app.use(createPinia())
app.use(router)
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: '.my-app-dark',
      cssLayer: false,
      prefix: 'p',
    },
  },
})
app.use(ToastService)
app.mount('#app')
