<style scoped lang="scss">
.wrap {
  width: 100%;
  height: 100%;
}
.search {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <div class="wrap">
    <DataTable
      v-model:filters="filters"
      :value="tableData"
      paginator
      showGridlines
      :rows="10"
      dataKey="id"
      filterDisplay="menu"
      :loading="loading"
      v-model:selection="selectedItem"
      :globalFilterFields="['name', 'overtimeTime.hours', 'overtimeTime.min', 'reason']"
    >
      <template #header>
        <div class="search">
          <Button
            type="button"
            icon="pi pi-filter-slash"
            label="Clear"
            outlined
            @click="clearFilter()"
          />
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText v-model="filters['global'].value" placeholder="Keyword Search" />
          </IconField>
        </div>
      </template>

      <template #empty> No customers found. </template>

      <template #loading> Loading customers data. Please wait. </template>
      <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
      <Column field="name" header="Name">
        <template #body="{ data }">
          {{ data.name }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search by name" />
        </template>
      </Column>

      <Column header="Overtime" filterField="overtimeTime.hours">
        <template #body="{ data }">
          {{ `${data.overtimeTime.hours} h ${data.overtimeTime.min} min` }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" />
        </template>
      </Column>

      <Column field="reason" header="Reason">
        <template #body="{ data }">
          {{ data.reason }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search reasons" />
        </template>
      </Column>
    </DataTable>
  </div>
</template>
<script setup lang="ts">
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { InputIcon, InputText, IconField, Button } from 'primevue'
import { ref, onMounted } from 'vue'
import { FilterMatchMode, FilterOperator } from '@primevue/core/api'

type OvertimeApprovalType = {
  name: string
  overtimeTime: {
    hours: number
    min: number
  }
  reason: string
}

const overtimeApprovalData: OvertimeApprovalType[] = [
  {
    name: 'John Smith',
    overtimeTime: {
      hours: 2,
      min: 30,
    },
    reason: "Emergency concrete pour that couldn't be delayed due to setting time requirements",
  },
  {
    name: 'Maria Garcia',
    overtimeTime: {
      hours: 1,
      min: 45,
    },
    reason: 'Steel delivery arrived late, needed to secure materials before leaving site',
  },
  {
    name: 'Robert Johnson',
    overtimeTime: {
      hours: 3,
      min: 15,
    },
    reason: 'Critical structural reinforcement before scheduled city inspection',
  },
  {
    name: 'Emily Chen',
    overtimeTime: {
      hours: 0,
      min: 45,
    },
    reason: 'Safety meeting ran over due to addressing recent near-miss incidents',
  },
  {
    name: 'David Williams',
    overtimeTime: {
      hours: 5,
      min: 0,
    },
    reason: 'Weekend foundation work required before forecasted freezing temperatures',
  },
  {
    name: 'Sarah Martinez',
    overtimeTime: {
      hours: 2,
      min: 15,
    },
    reason: 'Water line break requiring immediate repair before site closure',
  },
  {
    name: 'Michael Brown',
    overtimeTime: {
      hours: 1,
      min: 30,
    },
    reason: 'Training new crew members on specialized equipment operation',
  },
  {
    name: 'Jennifer Taylor',
    overtimeTime: {
      hours: 4,
      min: 0,
    },
    reason: 'Preparing site for severe weather conditions forecasted overnight',
  },
  {
    name: 'Thomas Anderson',
    overtimeTime: {
      hours: 2,
      min: 45,
    },
    reason: "Electrical system troubleshooting that couldn't be left incomplete",
  },
  {
    name: 'Lisa Rodriguez',
    overtimeTime: {
      hours: 3,
      min: 30,
    },
    reason: 'Final inspection preparation for client walkthrough scheduled for tomorrow',
  },
  {
    name: 'James Wilson',
    overtimeTime: {
      hours: 1,
      min: 15,
    },
    reason: 'Securing hazardous materials before weekend site closure',
  },
  {
    name: 'Patricia Lee',
    overtimeTime: {
      hours: 2,
      min: 0,
    },
    reason: 'Coordination with utility company for emergency service connection',
  },
  {
    name: 'Christopher Davis',
    overtimeTime: {
      hours: 3,
      min: 45,
    },
    reason: 'Scaffolding reinforcement due to unexpected wind conditions',
  },
  {
    name: 'Nancy Miller',
    overtimeTime: {
      hours: 0,
      min: 50,
    },
    reason: 'Documentation completion required for regulatory compliance submission',
  },
  {
    name: 'Daniel Jackson',
    overtimeTime: {
      hours: 4,
      min: 30,
    },
    reason: 'Emergency excavation work due to discovered underground obstruction',
  },
]

const tableData = ref()
const filters = ref()
const loading = ref(true)
const selectedItem = ref()

onMounted(() => {
  tableData.value = overtimeApprovalData
  loading.value = false
})

const initFilters = () => {
  filters.value = {
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    reason: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    'overtimeTime.hours': {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }],
    },
    'overtimeTime.min': {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }],
    },
  }
}
initFilters()

const clearFilter = () => {
  initFilters()
}
</script>
