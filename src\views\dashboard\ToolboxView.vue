<style lang="scss" scoped>
.table-cell {
  display: flex;
  align-items: center;
}
.ul {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  // list-style: none;
  // border: 1px red solid;
  width: 100%;
  padding-inline-start: 0;
  .li {
    font-size: 1.1rem;
    padding-block: 0.25rem;
  }
}
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-a {
  padding: 0.5rem;
  h3 {
    font-size: 1.5rem;
    font-weight: 900;
    text-decoration: underline;
    text-underline-offset: 6px;
    text-decoration-thickness: 3px;
  }
}
.left-pane {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
</style>
<template>
  <TimeManagementLayout>
    <Splitter style="height: 100%; padding: 0.75rem">
      <SplitterPanel class="flex items-center justify-center" :size="25" :minSize="10">
        <Card class="card-a">
          <template #header>
            <h3>Toolbox Topics</h3>
          </template>
          <template #content>
            <div class="left-pane">
              <ol class="ul">
                <li class="li" v-for="(item, idx) in toolboxTopics" :key="idx">{{ item }}</li>
              </ol>
              <Button
                label="Edit Tomorrows Topics"
                icon="pi pi-arrow-right"
                iconPos="right"
                severity="contrast"
                raised
                :pt="{
                  label: {
                    style: {
                      'font-size': '1.1rem',
                      'font-weight': 'bold',
                    },
                  },
                }"
              />
            </div>
          </template>
        </Card>
      </SplitterPanel>
      <SplitterPanel class="flex items-center justify-center" :size="75">
        <div>
          <Card>
            <template #content>
              <DataTable
                stripedRows
                showGridlines
                v-model:filters="filters"
                :value="toolboxAttendees"
                paginator
                :rows="10"
                dataKey="id"
                filterDisplay="row"
                :loading="loading"
                :globalFilterFields="['fullName', 'joiningTime']"
                :size="isBasicSidebarOpen || screenSize < 1300 ? 'small' : undefined"
              >
                <template #header>
                  <div class="table-header">
                    <h2>Attendees</h2>
                    <IconField>
                      <InputIcon>
                        <i class="pi pi-search" />
                      </InputIcon>
                      <InputText v-model="filters['global'].value" placeholder="Keyword Search" />
                    </IconField>
                  </div>
                </template>

                <template #empty> No worker found. </template>

                <template #loading> Loading worker data. Please wait. </template>

                <Column field="fullName" header="Name" style="">
                  <template #body="{ data }">
                    {{ data.fullName }}
                  </template>

                  <template #filter="{ filterModel, filterCallback }">
                    <InputText
                      v-model="filterModel.value"
                      type="text"
                      @input="filterCallback()"
                      placeholder="Search by name"
                    />
                  </template>
                </Column>

                <Column header="Joining Time" style="">
                  <template #body="{ data }">
                    <div class="table-cell">
                      <span>{{ data.joiningTime.toLocaleTimeString() }}</span>
                    </div>
                  </template>
                </Column>
              </DataTable>
            </template>
          </Card>
        </div>
      </SplitterPanel>
    </Splitter>
  </TimeManagementLayout>
</template>
<script setup lang="ts">
import TimeManagementLayout from '@/layouts/TimeManagementLayout.vue'
import Splitter from 'primevue/splitter'
import SplitterPanel from 'primevue/splitterpanel'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { IconField, InputIcon, InputText, Card, Button } from 'primevue'
import { FilterMatchMode } from '@primevue/core/api'
import { ref, onMounted } from 'vue'
import { useLayoutStore } from '@/stores/LayoutStore'
import { storeToRefs } from 'pinia'

type ToolboxAttendeeType = {
  fullName: string
  joiningTime: Date
}

const toolboxAttendees = ref<ToolboxAttendeeType[]>([])
const loading = ref(true)
const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS },
  fullName: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
})
const screenSize = ref(window.innerWidth)
const store = useLayoutStore()
const { isBasicSidebarOpen } = storeToRefs(store)

const toolboxTopics = [
  'Personal Protective Equipment',
  'Working at Heights',
  'Manual Handling & Lifting Techniques',
  'Slips, Trips, and Falls',
  'Electrical Safety',
  'Heavy Equipment Safety',
  'Emergency Procedures & First Aid',
]

const toolboxAttendeesSample: ToolboxAttendeeType[] = [
  {
    fullName: 'Sarah Johnson',
    joiningTime: new Date('2025-02-21T09:00:00Z'),
  },
  {
    fullName: 'Michael Chen',
    joiningTime: new Date('2025-02-21T09:01:30Z'),
  },
  {
    fullName: 'Emma Williams',
    joiningTime: new Date('2025-02-21T09:03:15Z'),
  },
  {
    fullName: 'James Rodriguez',
    joiningTime: new Date('2025-02-21T09:05:00Z'),
  },
  {
    fullName: 'Priya Patel',
    joiningTime: new Date('2025-02-21T09:07:45Z'),
  },
  {
    fullName: 'David Kim',
    joiningTime: new Date('2025-02-21T09:10:00Z'),
  },
  {
    fullName: 'Olivia Martinez',
    joiningTime: new Date('2025-02-21T09:12:30Z'),
  },
  {
    fullName: 'Alexander Singh',
    joiningTime: new Date('2025-02-21T09:15:45Z'),
  },
  {
    fullName: 'Sophie Anderson',
    joiningTime: new Date('2025-02-21T09:18:20Z'),
  },
  {
    fullName: 'Thomas Wright',
    joiningTime: new Date('2025-02-21T09:20:00Z'),
  },
]

onMounted(() => {
  toolboxAttendees.value = toolboxAttendeesSample
  loading.value = false
  window.addEventListener('resize', () => {
    screenSize.value = window.innerWidth
  })
})
</script>
