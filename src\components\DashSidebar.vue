<style scoped lang="scss">
.card {
  width: fit-content;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px var(--p-panelmenu-panel-border-color) solid;
  padding: 0rem 0.25rem;

  .p-panelmenu {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer/Edge */
    display: flex !important;
    flex-direction: column !important;
    gap: 4px;
  }

  .p-panelmenu::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .p-panelmenu {
    width: 100%;
    height: 100%;
    padding-inline: 0.25rem;
    overflow-y: scroll;
  }
  .light {
    color: var(--p-surface-700);
  }
  .dark {
    color: var(--p-surface-0);
  }
  .panel-item {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem 1rem;
    text-decoration: none;
  }

  .panel {
    // background-color: transparent !important;
    display: flex;
    flex-direction: column;
  }
  :deep(.panel) {
    background-color: transparent;
    border: 0;
  }
  :deep(.panel-item) {
    color: var(--p-text-color);
  }
  .p-panelmenu-submenu {
    display: flex !important;
    flex-direction: column; /* Change as needed */
    gap: 10px; /* Adds spacing between items */
  }

  .left {
    margin-left: auto;
  }
  .title {
    font-size: 2.5rem;
    width: fit-content;
    margin-bottom: 1rem;
    padding-inline: 0.5rem;
    color: var(--p-text-color);
  }
  .item-label {
    font-size: 0.95rem;
    font-weight: 600;
  }
  .menu-item {
    border-left: 8px solid transparent;
    border-radius: 4px;
  }
  .focused-item {
    border-left: 8px solid var(--p-primary-color);
    color: var(--p-menu-item-focus-color);
    background-color: var(--p-menu-item-focus-background);
  }
  .font-bold {
    font-weight: 1.2rem;
  }
}
</style>

<template>
  <div class="card flex justify-center">
    <RouterLink
      to="/"
      class="flex mb-2 px-2 py-2"
      style="
        text-align: right;
        display: flex;
        margin-bottom: 0.5rem;
        padding: 1rem;
        text-decoration: none;
      "
    >
      <img src="../assets/img/preview-removebg-preview.png" alt="logo" width="70" height="70" />
      <h1 class="title">Laxmi</h1>
    </RouterLink>
    <PanelMenu
      :model="menuItems"
      class=""
      :pt="{
        panel: {
          class: 'panel',
        },
      }"
    >
      <template #item="{ item }">
        <router-link
          v-if="item.route"
          v-slot="{ href, navigate }"
          :to="{ name: item.route }"
          custom
        >
          <a
            class="panel-item flex items-center cursor-pointer text-surface-700 dark:text-surface-0 px-4 py-2"
            :href="href"
            @click="navigate"
          >
            <span v-if="item.icon" :class="item.icon" />
            <span v-if="item.customIcon">
              <component :is="item.customIcon" class="" style="width: 1rem; height: 1rem" />
            </span>
            <span class="item-label">{{ item.label }}</span>
            <span v-if="item.items" class="pi pi-angle-down text-primary ml-auto" />
          </a>
        </router-link>

        <a
          class="panel-item flex items-center cursor-pointer text-surface-700 dark:text-surface-0 px-4 py-2"
          :href="item.url"
          :target="item.target"
          v-else
        >
          <span v-if="item.icon" :class="item.icon" style="font-size: 1.3rem" />
          <span v-if="item.customIcon">
            <component :is="item.customIcon" class="mr-2" style="width: 1.5rem; height: 1.5rem" />
          </span>
          <span class="item-label">{{ item.label }}</span>
          <span v-if="item.items" class="pi pi-angle-down text-primary ml-auto left" />
        </a>
      </template>
    </PanelMenu>
  </div>
</template>

<script setup lang="ts">
import { shallowRef } from 'vue'
import { PanelMenu } from 'primevue'

import WorkerIcon from '@/icons/WorkerIcon.vue'
import HSEIcon from '@/icons/HSEIcon.vue'
import Form1Icon from '@/icons/Form1Icon.vue'
import Helmet1Icon from '@/icons/Helmet1Icon.vue'

const menuItems = shallowRef([
  {
    label: 'Home',
    icon: 'pi pi-home',
    route: 'dashHome',
  },
  {
    label: 'Time Management',
    icon: 'pi pi-clock',
    route: 'attendance',
  },
  {
    label: 'Workers',
    customIcon: WorkerIcon,
    route: 'workers',
  },
  {
    label: 'HSE',
    customIcon: HSEIcon,
    items: [
      {
        label: 'Overview',
        // customIcon: HSEIcon,
      },
      {
        label: 'Incidents',
        // customIcon: HSEIcon,
      },
      {
        label: 'Training Management',
        route: 'trainings',
        // customIcon: HSEIcon,
      },
      {
        label: 'Audits',
        // customIcon: HSEIcon,
      },
      {
        label: 'Observation',
        // customIcon: HSEIcon,
      },
      {
        label: 'Risk',
        // customIcon: HSEIcon,
      },
    ],
  },
  {
    label: 'Forms',
    customIcon: Form1Icon,
    items: [
      {
        label: 'Training',
        // customIcon: Form1Icon,
      },
      {
        label: 'Introduction',
        // customIcon: Form1Icon,
      },
      {
        label: 'Safety Inspections',
        // customIcon: Form1Icon,
      },
      {
        label: 'Equipment Inspections',
        // customIcon: Form1Icon,
      },
      {
        label: 'Vehicle Inspections',
        // customIcon: Form1Icon,
      },
      {
        label: 'Critical Work',
        // customIcon: Form1Icon,
      },
      {
        label: 'Incident Management',
        // customIcon: Form1Icon,
      },
      {
        label: 'Daily Log',
        // customIcon: Form1Icon,
      },
      {
        label: 'Fall Protection',
        // customIcon: Form1Icon,
      },
      {
        label: 'Job Planning',
        // customIcon: Form1Icon,
      },
    ],
  },
  {
    label: 'Tasks',
    icon: 'pi pi-list',
  },
  {
    label: 'PPE',
    customIcon: Helmet1Icon,
  },
])
</script>
