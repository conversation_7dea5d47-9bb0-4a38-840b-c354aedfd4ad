<style scoped lang="scss">
.container-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 1rem;
}
.forms {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  & > * {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}
.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}
.btn {
  align-self: flex-start;
}
</style>

<template>
  <div v-if="!id">Id is required</div>
  <div v-else>
    <!-- <div v-if="workerByIdError">
      <p>Error occured while getting worker details,</p>
      <p>
        {{ workerByIdError }}
      </p>
    </div> -->

    <div v-if="workerByIdLoading">
      <ProgressSpinner />
    </div>

    <div v-else>
      <div class="container-form">
        <h2>Update {{ workerName.length > 0 ? `${workerName}'s` : '' }} details</h2>
        <p>Fields with * are compulsory</p>
        <Form
          v-slot="$form"
          :resolver="resolver"
          :initialValues="computedInitialValues"
          @submit="onFormSubmit"
          class="form grid lg:grid-cols-2 gap-4 w-full"
        >
          <div class="forms">
            <div class="">
              <div class="form-group">
                <label for="name" class="mb-1"> Name <span class="text-red-500">*</span> </label>
                <InputText
                  id="name"
                  name="name"
                  type="text"
                  placeholder="Enter your full name"
                  fluid
                />
                <Message v-if="$form.name?.invalid" severity="error" size="small" variant="simple">
                  {{ $form.name.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="nationalId" class="mb-1">
                  National ID <span class="text-red-500">*</span>
                </label>
                <InputText
                  id="nationalId"
                  name="nationalId"
                  type="text"
                  placeholder="Enter national ID"
                  fluid
                />
                <Message
                  v-if="$form.nationalId?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.nationalId.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="gender" class="mb-1">
                  Gender <span class="text-red-500">*</span>
                </label>
                <SelectButton id="gender" name="gender" :options="options" />
                <Message v-if="$form.gender?.invalid" severity="error">
                  {{ $form.gender.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="age" class="mb-1"> Age <span class="text-red-500">*</span> </label>
                <InputNumber id="age" name="age" type="number" placeholder="Enter age" fluid />
                <Message v-if="$form.age?.invalid" severity="error" size="small" variant="simple">
                  {{ $form.age.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="phoneNumber" class="mb-1"> Phone Number </label>
                <InputText
                  id="phoneNumber"
                  name="phoneNumber"
                  type="text"
                  placeholder="Enter phone number"
                  fluid
                />
                <Message
                  v-if="$form.phoneNumber?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.phoneNumber.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="email" class="mb-1"> Email </label>
                <InputText
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter email address"
                  fluid
                />
                <Message v-if="$form.email?.invalid" severity="error" size="small" variant="simple">
                  {{ $form.email.error?.message }}
                </Message>
              </div>
              <div style="display: flex; gap: 2rem">
                <div>
                  <img src="https://picsum.photos/id/237/200/300" alt="placeholder image" />
                </div>
                <div style="display: flex; flex-direction: column; margin-block: auto; gap: 2rem">
                  <div
                    class="form-group card flex flex-col gap-6 items-center justify-center"
                    style="align-self: self-start"
                  >
                    <FileUpload
                      ref="fileupload"
                      mode="basic"
                      name="demo[]"
                      url="/api/upload"
                      accept="image/*"
                      :maxFileSize="1000000"
                      @upload="onUpload"
                    />
                  </div>
                  <div>
                    <Button
                      icon="pi pi-camera"
                      label="Capture with HikVision"
                      severity="info"
                      @click="
                        () =>
                          toast.add({
                            severity: 'error',
                            summary: 'unable to capture',
                            detail: 'Hikvision has not been intergrated',
                          })
                      "
                    />
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="form-group">
                <label for="company" class="mb-1">
                  Company <span class="text-red-500">*</span>
                </label>
                <InputText
                  id="company"
                  name="company"
                  type="text"
                  placeholder="Enter company name"
                  fluid
                />
                <Message
                  v-if="$form.company?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.company.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="trade" class="mb-1"> Trade <span class="text-red-500">*</span> </label>
                <InputText id="trade" name="trade" type="text" placeholder="Enter trade" fluid />
                <Message v-if="$form.Trade?.invalid" severity="error" size="small" variant="simple">
                  {{ $form.Trade.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="skills" class="mb-1">Skills<span class="text-red-500">*</span> </label>
                <MultiSelect
                  name="skills"
                  :options="skillsDummyData"
                  optionLabel="name"
                  optionValue="name"
                  filter
                  :loading="false"
                  :placeholder="false ? 'Loading...' : 'Select Skills'"
                  class="w-full md:w-80"
                >
                  <template #footer>
                    <Button
                      style="margin: 8px 8px"
                      label="Add New"
                      severity="secondary"
                      variant="outlined"
                      size="small"
                      icon="pi pi-plus"
                      v-show="!toggleSkillCreateMode"
                      @click="() => (toggleSkillCreateMode = !toggleSkillCreateMode)"
                    />
                    <div
                      v-show="toggleSkillCreateMode"
                      style="display: flex; flex-direction: column; width: 100%; margin: 8px 8px"
                    >
                      <div>
                        <i
                          class="pi pi-times"
                          @click="() => (toggleSkillCreateMode = !toggleSkillCreateMode)"
                          style="margin-right: 8px; cursor: pointer"
                        ></i>
                        <InputText size="small" placeholder="Carpentry" style="margin-right: 8px" />
                        <Button
                          style="margin: 4px"
                          label="Add New"
                          severity="primary"
                          variant="outlined"
                          size="small"
                          icon="pi pi-plus"
                        />
                      </div>
                      <Message v-if="false" size="small" severity="error" variant="simple">
                        {{ 'something is wrong' }}
                      </Message>
                    </div>
                  </template>
                </MultiSelect>
                <Message
                  v-if="$form.trainingIds?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.trainingIds.error?.message }}
                </Message>
              </div>

              {{}}

              <div class="form-group">
                <label for="trainingIds" class="mb-1">
                  Trainings
                  <span class="text-red-500">*</span>
                </label>
                <MultiSelect
                  name="trainingIds"
                  :options="trainingsDummyData"
                  optionLabel="name"
                  optionValue="id"
                  filter
                  :loading="false"
                  :placeholder="false ? 'Loading...' : 'Select Trainings'"
                  class="w-full md:w-80"
                >
                  <template #footer>
                    <Button
                      style="margin: 8px 8px"
                      label="Add New"
                      severity="secondary"
                      variant="outlined"
                      size="small"
                      icon="pi pi-plus"
                      v-show="!toggleTrainingCreateMode"
                      @click="() => (toggleTrainingCreateMode = !toggleTrainingCreateMode)"
                    />
                    <div
                      v-show="toggleTrainingCreateMode"
                      style="display: flex; flex-direction: column; width: 100%; margin: 8px 8px"
                    >
                      <div>
                        <i
                          class="pi pi-times"
                          @click="() => (toggleTrainingCreateMode = !toggleTrainingCreateMode)"
                          style="margin-right: 8px; cursor: pointer"
                        ></i>
                        <InputText size="small" placeholder="Carpentry" style="margin-right: 8px" />
                        <Button
                          style="margin: 4px"
                          label="Add New"
                          severity="primary"
                          variant="outlined"
                          size="small"
                          icon="pi pi-plus"
                        />
                      </div>
                      <Message v-if="false" size="small" severity="error" variant="simple">
                        {{ 'something is wrong' }}
                      </Message>
                    </div>
                  </template>
                </MultiSelect>
                <Message
                  v-if="$form.skills?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.skills.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="manHours" class="mb-1">
                  Man Hours <span class="text-red-500">*</span>
                </label>
                <InputNumber
                  id="manHours"
                  name="manHours"
                  type="number"
                  placeholder="Enter man hours"
                  fluid
                />
                <Message
                  v-if="$form.manHours?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.manHours.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="rating" class="mb-1">
                  Rating <span class="text-red-500">*</span>
                </label>
                <InputNumber
                  id="rating"
                  name="rating"
                  type="text"
                  placeholder="Enter rating"
                  fluid
                />
                <Message
                  v-if="$form.rating?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.rating.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="inductionDate" class="mb-1"> Induction Date </label>
                <DatePicker id="inductionDate" name="inductionDate" fluid />
                <Message
                  v-if="$form.inductionDate?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.inductionDate.error?.message }}
                </Message>
              </div>

              <div class="form-group">
                <label for="medicalCheckDate" class="mb-1"> Medical Check Date </label>
                <DatePicker id="medicalCheckDate" name="medicalCheckDate" fluid />
                <Message
                  v-if="$form.medicalCheckDate?.invalid"
                  severity="error"
                  size="small"
                  variant="simple"
                >
                  {{ $form.medicalCheckDate.error?.message }}
                </Message>
              </div>
            </div>
          </div>

          <Button
            type="submit"
            severity="info"
            variant="outlined"
            label="Submit"
            size="large"
            class="btn"
          />
        </Form>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import InputNumber from 'primevue/inputnumber'
import DatePicker from 'primevue/datepicker'
import { Form, type FormSubmitEvent } from '@primevue/forms'
import { InputText, Message, Button } from 'primevue'
import SelectButton from 'primevue/selectbutton'
import MultiSelect from 'primevue/multiselect'
import {
  useWorkerById,
  toUpdateWorkerParameter,
  type WorkerByIdType,
} from '@/graphQL/Queries/useWorkerId'
import { useRoute } from 'vue-router'
import ProgressSpinner from 'primevue/progressspinner'
import { useToast } from 'primevue'
import { computed, ref, watch } from 'vue'
import { type UpdateWorkerParameterType } from '@/graphQL/Mutations/useUpdateWorker'
import { z } from 'zod'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import FileUpload from 'primevue/fileupload'

const route = useRoute()
const id = route.params.id
const idNum = typeof id === 'string' ? Number(id) : Number(id[0])
// const render = ref(false)
const {
  // result: workerByIdResult,
  onResult: workerByIdOnResult,
  loading: workerByIdLoading,
  // error: workerByIdError,
  onError: workerByIdOnError,
} = useWorkerById(idNum)
const workerName = ref('hello')
const skillsDummyData = [
  {
    id: 12,
    name: 'capentry',
  },
  {
    id: 13,
    name: 'finishing',
  },
  {
    id: 14,
    name: 'cleaning',
  },
  {
    id: 15,
    name: 'wiring',
  },
  {
    id: 16,
    name: 'troubleshooting',
  },
  {
    id: 17,
    name: 'blueprint reading',
  },
]

const trainingsDummyData = [
  {
    id: 1,
    name: 'carpentry',
  },
  {
    id: 2,
    name: 'finnishing',
  },
  {
    id: 5,
    name: 'fitting',
  },
  {
    id: 3,
    name: 'mansory',
  },
  {
    id: 4,
    name: 'safety',
  },
]

const toggleSkillCreateMode = ref(false)
const toggleTrainingCreateMode = ref(false)

const toast = useToast()
workerByIdOnError((err) => {
  toast.add({
    severity: 'error',
    summary: `Error in getting worker details`,
    detail: `${err.message}`,
    life: 3000,
  })
})

const initialValues = ref<UpdateWorkerParameterType>()
const resolver = ref(
  zodResolver(
    z.object({
      id: z.number().int().positive('ID must be a positive integer.'),
      name: z.string().min(1, 'Name is required.'), //.nullable(), //.optional(),
      company: z.string().min(1, 'Company is required.'), //.nullable(), //.optional(),
      trade: z.string().min(1, 'Trade is required.'), //.nullable(), //.optional(),
      skills: z
        .array(
          z.object({
            id: z.string().min(1, 'A skill is required.'),
          }),
        )
        .min(1, 'A skill is required.'),
      trainingIds: z
        .array(
          z.object({
            id: z.string().min(1, 'Training is required.'),
          }),
        )
        .min(1, 'Training is required.'),
      manHours: z.number().int().min(0, 'Man hours must be a non-negative number.'), //.nullable(),
      rating: z.number().min(0, 'Rating must be at least 0.').max(5, 'Rating cannot exceed 5.'),
      gender: z.string().nullable(),
      phoneNumber: z.string().min(5, { message: 'Must be 5 or more characters long' }),
      email: z.string().email('Invalid email format.').nullable(),
      inductionDate: z.date(),
      medicalCheckDate: z.date(),
    }),
  ),
)

const options = ['Male', 'Female']

workerByIdOnResult((result) => {
  if (!result.data) {
    const worker: WorkerByIdType = {
      id: 1,
      name: 'John Doe',
      company: 'ABC Construction Ltd.',
      trade: 'Electrician',
      skills: ['wiring', 'troubleshooting', 'blueprint reading'],
      nationalId: 'NID123456789',
      gender: 'Male',
      age: 35,
      trainingIds: [1, 2, 4],
      manHours: 1500,
      rating: 4.5,
      createdBy: 'admin',
      createdAt: '2023-01-15T10:30:00Z',
      photoUrl: 'https://example.com/photos/john-doe.jpg',
      phoneNumber: '+**********',
      email: '<EMAIL>',
      inductionDate: '2022-12-01T00:00:00Z',
      medicalCheckDate: '2023-01-01T00:00:00Z',
      updatedAt: '2024-06-20T15:00:00Z',
      updatedBy: 'hr_manager',
    }
    result.data = worker
    // return
  }

  initialValues.value = toUpdateWorkerParameter(result.data)
})

watch(
  initialValues,
  (val) => {
    if (val?.name) {
      workerName.value = val.name
      console.log(workerName.value)
    }
  },
  { immediate: false },
)

const computedInitialValues = computed(() => {
  return initialValues.value
})

const onFormSubmit = (event: FormSubmitEvent) => {
  if (event.valid) {
    console.log(event.values)
  }
}

const fileupload = ref()

const onUpload = () => {
  // toast.add({ severity: 'info', summary: 'Success', detail: 'File Uploaded', life: 3000 });
}
</script>
