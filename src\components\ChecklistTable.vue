<template>
  <Card>
    <template #title>
      <p style="font-weight: 900; font-size: 1.6rem">Checklist</p>
    </template>
    <template #content>
      <DataTable :value="tableData" tableStyle="min-width: 25rem" showGridlines stripedRows>
        <Column field="name" header="Name" sortable></Column>
        <Column field="count" header="Count" sortable></Column>
      </DataTable>
    </template>
  </Card>
</template>
<script setup lang="ts">
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { Card } from 'primevue'
import { ref } from 'vue'

const data = [
  {
    name: 'Lexis Intl',
    count: 20,
  },
  {
    name: 'IFSS',
    count: 10,
  },
  {
    name: 'Giza System',
    count: 17,
  },
  {
    name: 'Huawei',
    count: 50,
  },
]

const tableData = ref(data)
</script>
