using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class Worker : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Company { get; set; }
        public DateOnly? DateOfBirth { get; set; }

        public int ManHours { get; set; }
        public string? PhotoUrl { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public double Rating { get; set; }
        public string Gender { get; set; }
        public string NationalId { get; set; }
        public string PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? MpesaNumber { get; set; }

        // Navigation Properties
        public ICollection<Training> Trainings { get; set; } = new List<Training>();
        public ICollection<Trade> Trades { get; set; } = new List<Trade>();
        public ICollection<Skill> Skills { get; set; } = new List<Skill>();
        public ICollection<WorkerTrainingHistory> TrainingHistory { get; set; } = new List<WorkerTrainingHistory>();

        // Computed Properties
        public int? Age => DateOfBirth.HasValue
            ? DateTime.Today.Year - DateOfBirth.Value.Year - (DateTime.Today.DayOfYear < DateOfBirth.Value.DayOfYear ? 1 : 0)
            : null;

        public int TrainingsCompleted => Trainings?.Count ?? 0;

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}