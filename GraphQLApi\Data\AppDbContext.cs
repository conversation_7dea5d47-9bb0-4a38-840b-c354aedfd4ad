using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using System.Reflection;
using Shared.Interfaces;
using System.Linq.Expressions;

namespace GraphQLApi.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Worker> Workers { get; set; }
        public DbSet<WorkerAttendance> WorkerAttendances { get; set; }
        public DbSet<ToolboxSession> ToolboxSessions { get; set; }
        public DbSet<ToolboxAttendance> ToolboxAttendances { get; set; }
        public DbSet<Training> Trainings { get; set; }
        public DbSet<Trade> Trades { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<WorkerTrainingHistory> WorkerTrainingHistory { get; set; }
        public DbSet<Shared.GraphQL.Models.Task> Tasks { get; set; }
        public DbSet<Equipment> Equipment { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Apply global query filters for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)
                        ?.MakeGenericMethod(entityType.ClrType);
                    var filter = method?.Invoke(null, Array.Empty<object>());
                    entityType.SetQueryFilter((LambdaExpression)filter!);
                }
            }

            base.OnModelCreating(modelBuilder);
        }

        private static LambdaExpression GetSoftDeleteFilter<TEntity>() where TEntity : class, ISoftDeletable
        {
            Expression<Func<TEntity, bool>> filter = x => !x.IsDeleted;
            return filter;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            HandleSoftDelete();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is IAuditableEntity && (
                    e.State == EntityState.Added ||
                    e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (IAuditableEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                    entity.CreatedBy ??= "System";
                }

                entity.UpdatedAt = DateTime.UtcNow;
                entity.UpdatedBy ??= "System";
            }
        }

        private void HandleSoftDelete()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is ISoftDeletable && e.State == EntityState.Deleted);

            foreach (var entityEntry in entries)
            {
                var entity = (ISoftDeletable)entityEntry.Entity;
                entityEntry.State = EntityState.Modified;
                entity.IsDeleted = true;
                entity.DeletedAt = DateTime.UtcNow;
                entity.DeletedBy ??= "System";
            }
        }
    }
}