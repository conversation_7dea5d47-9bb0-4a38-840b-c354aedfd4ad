<style lang="scss" scoped></style>
<template>
  <Card>
    <template #title>
      <p style="font-weight: 900; font-size: 1.6rem">Trades</p>
    </template>
    <template #content>
      <DataTable :value="tableData" size="large" showGridlines stripedRows>
        <Column field="name" header="Name" sortable></Column>
        <Column field="count" header="Count" sortable></Column>
        <Column field="contractor" header="Contractor" sortable></Column>
      </DataTable>
    </template>
  </Card>
  <!-- <div class="wrapper">
    <div>
    </div>
  </div> -->
</template>
<script setup lang="ts">
import { reactive } from 'vue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { Card } from 'primevue'

const tradeData = [
  {
    name: 'Concrete Work',
    count: 40,
    contractor: '',
  },
  {
    name: 'Plumbing',
    count: 20,
    contractor: '',
  },
  {
    name: 'Scaffolding',
    count: 17,
    contractor: '',
  },
  {
    name: 'Ceiling FIxing',
    count: 20,
    contractor: '',
  },
]

const tableData = reactive(tradeData)
</script>
