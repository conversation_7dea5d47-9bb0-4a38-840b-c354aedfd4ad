<style scoped lang="scss">
.card {
  width: fit-content;
  height: 100%;
  .title {
    font-size: 2.5rem;
    width: fit-content;
    margin-bottom: 1rem;
    // margin-top: auto;
    // padding: 0;
    padding-inline: 0.5rem;
  }
  .item-label {
    font-size: 1.2rem;
    font-weight: bold;
  }
  .menu-item {
    border-left: 8px solid transparent;
    border-radius: 4px;
  }
  .focused-item {
    border-left: 8px solid var(--p-primary-color);
    color: var(--p-menu-item-focus-color);
    background-color: var(--p-menu-item-focus-background);
  }
  .font-bold {
    font-weight: 1.2rem;
  }
}
</style>

<template>
  <div class="card flex justify-center" style="">
    <Menu :model="items" style="height: 100%; padding-right: 1.5rem; border-color: transparent">
      <template #start>
        <span
          class="flex mb-2 px-2 py-2"
          style="
            text-align: right;
            display: flex;
            /* align-items: flex-end; */
            /* padding: 0.5rem; */
            margin-bottom: 0.5rem;
          "
        >
          <img src="../assets/img/preview-removebg-preview.png" alt="logo" width="70" height="70" />
          <h1 class="title">Laxmi</h1>
        </span>
      </template>

      <template #submenuitem="{ item }">
        <span class="text-primary font-bold">{{ item.label }}</span>
      </template>
      <template #item="{ item, props }">
        <template v-if="item.route">
          <a
            href="#"
            v-bind="props.action"
            @click="handleClickItem(item.label)"
            :class="{ 'focused-item': clickedItem === item.label }"
            class="menu-item"
          >
            <span v-if="item.customIcon">
              <component :is="item.customIcon" class="mr-2" style="width: 1rem; height: 1rem" />
            </span>
            <span v-else-if="item.icon">
              <span :class="item.icon" style="width: 1rem; height: 1rem" />
            </span>
            <span class="item-label">{{ item.label }}</span>
          </a>
        </template>

        <a
          v-else
          :href="item.url"
          :target="item.target"
          v-bind="props.action"
          @click="handleClickItem(item.label)"
          :class="{ 'focused-item': clickedItem === item.label }"
          class="menu-item"
        >
          <span
            v-if="item.customIcon"
            style="display: flex; align-items: center; justify-content: center"
          >
            <component :is="item.customIcon" class="" style="width: 1rem; height: 1rem" />
          </span>
          <span v-else-if="item.icon">
            <span :class="item.icon" style="width: 1rem; height: 1rem" />
          </span>
          <span class="item-label" style="">{{ item.label }}</span>
        </a>
      </template>
    </Menu>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, ref } from 'vue'
import { Menu } from 'primevue'
// import ExchangeIcon from '@/icons/ExchangeIcon.vue'
// import ExchangeOutline from '@/icons/ExchangeOutline.vue'
import Exchange3Icon from '@/icons/Exchange3Icon.vue'
import FileSafety from '@/icons/FileSafety.vue'

const clickedItem = ref('')

type LabelType = string | undefined | (() => string)

function handleClickItem(label: LabelType) {
  if (typeof label === 'function') {
    const labelText = label()
    clickedItem.value = labelText
  } else if (typeof label === 'string') {
    clickedItem.value = label
  }
}

const items = shallowRef([
  {
    label: 'General',
    items: [
      {
        label: 'Finance',
        customIcon: Exchange3Icon,
        // icon: 'pi pi-wallet',
      },
      {
        label: 'Security',
        customIcon: FileSafety,
      },
    ],
  },
])
</script>
