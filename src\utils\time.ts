export interface TimeData {
  hours: number
  minutes: number
  seconds?: number
}

export class TimeDataManager implements TimeData {
  private hours_: number = 0
  private minutes_: number = 0
  private seconds_: number = 0
  constructor(data: TimeData) {
    this.hours_ = data.hours
    this.minutes_ = data.minutes
    this.seconds_ = data.seconds ?? 0
  }
  get minutes() {
    return this.minutes_
  }
  get hours() {
    return this.hours_
  }
  get seconds() {
    return this.seconds_
  }

  set hours(h: number) {
    this.hours_ = h
  }
  set minutes(m: number) {
    if (!this.checkValidMinOrSec(m)) {
      // throw new Error('minutes is not valid')
      console.error('Minutes is not a valid value')
      return
    }
    this.minutes = m
  }
  set seconds(s: number) {
    if (!this.checkValidMinOrSec(s)) {
      console.error('seconds is not a valid value')
      return
    }
    this.seconds_ = s
  }

  checkValidMinOrSec(t: number): boolean {
    return t >= 0 && t <= 60
  }
}
