<style lang="scss" scoped>
.text-green-500 {
  color: var(--p-green-500);
}
.text-red-500 {
  color: var(--p-red-500);
}
.wrap {
  width: 100%;
  height: 100%;
}
.search {
  display: flex;
  justify-content: space-between;
}
</style>
<template>
  <div class="wrap">
    <DataTable
      v-model:filters="filters"
      :value="overtimeTableData"
      paginator
      showGridlines
      :rows="10"
      dataKey="id"
      filterDisplay="menu"
      :loading="loading"
      :globalFilterFields="[
        'name',
        'overtimeTime.hours',
        'overtimeTime.min',
        'aaccepted',
        'reason',
      ]"
    >
      <template #header>
        <div class="search">
          <Button
            type="button"
            icon="pi pi-filter-slash"
            label="Clear"
            outlined
            @click="clearFilter()"
          />
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText v-model="filters['global'].value" placeholder="Keyword Search" />
          </IconField>
        </div>
      </template>

      <template #empty> No customers found. </template>

      <template #loading> Loading customers data. Please wait. </template>

      <Column field="name" header="Name">
        <template #body="{ data }">
          {{ data.name }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search by name" />
        </template>
      </Column>

      <Column header="Overtime" filterField="overtimeTime.hour">
        <template #body="{ data }">
          {{ `${data.overtimeTime.hour} h ${data.overtimeTime.min} min` }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" />
        </template>
      </Column>

      <Column field="reason" header="Reason">
        <template #body="{ data }">
          {{ data.reason }}
        </template>
        <template #filter="{ filterModel }">
          <InputText v-model="filterModel.value" type="text" placeholder="Search reasons" />
        </template>
      </Column>

      <Column field="accepted" header="Status" dataType="boolean" bodyClass="text-center">
        <template #body="{ data }">
          <i
            class="pi"
            :class="{
              'pi-check-circle text-green-500 ': data.accepted,
              'pi-times-circle text-red-500': !data.accepted,
            }"
          ></i>
        </template>
        <template #filter="{ filterModel }">
          <label for="verified-filter" class="font-bold"> Verified </label>
          <Checkbox
            v-model="filterModel.value"
            :indeterminate="filterModel.value === null"
            binary
            inputId="accepted-filter"
          />
        </template>
      </Column>
    </DataTable>
  </div>
</template>
<script setup lang="ts">
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { InputIcon, InputText, IconField, Button, Checkbox } from 'primevue'
import { ref, onMounted } from 'vue'
import { FilterMatchMode, FilterOperator } from '@primevue/core/api'

type TableDataType = {
  name: string
  overtimeTime: {
    hour: number
    min: number
  }
  accepted: boolean
  reason: string
}

const tableData: TableDataType[] = [
  {
    name: 'John Smith',
    overtimeTime: {
      hour: 2,
      min: 30,
    },
    accepted: true,
    reason: "Emergency concrete pour that couldn't be delayed",
  },
  {
    name: 'Sarah Johnson',
    overtimeTime: {
      hour: 1,
      min: 45,
    },
    accepted: false,
    reason: 'Rescheduled due to unexpected rain forecast',
  },
  {
    name: 'Michael Chen',
    overtimeTime: {
      hour: 3,
      min: 15,
    },
    accepted: true,
    reason: 'Critical structural reinforcement before inspection',
  },
  {
    name: 'Emily Rodriguez',
    overtimeTime: {
      hour: 0,
      min: 45,
    },
    accepted: true,
    reason: 'Equipment delivery arrived late in the day',
  },
  {
    name: 'David Kim',
    overtimeTime: {
      hour: 5,
      min: 0,
    },
    accepted: true,
    reason: 'Weekend work to complete foundation before frost',
  },
  {
    name: 'Lisa Wong',
    overtimeTime: {
      hour: 2,
      min: 0,
    },
    accepted: false,
    reason: "Permit approval delayed, couldn't proceed with work",
  },
  {
    name: 'Robert Taylor',
    overtimeTime: {
      hour: 1,
      min: 30,
    },
    accepted: true,
    reason: 'Training new crew on safety protocols for scaffolding',
  },
  {
    name: 'Jessica Brown',
    overtimeTime: {
      hour: 4,
      min: 15,
    },
    accepted: false,
    reason: "Subcontractor couldn't provide necessary support staff",
  },
  {
    name: 'Thomas Garcia',
    overtimeTime: {
      hour: 2,
      min: 45,
    },
    accepted: true,
    reason: 'Emergency repair of damaged water line on site',
  },
  {
    name: 'Olivia Martinez',
    overtimeTime: {
      hour: 3,
      min: 30,
    },
    accepted: true,
    reason: 'Final site cleanup before client walkthrough',
  },
]

const overtimeTableData = ref()
const filters = ref()
const loading = ref(true)
onMounted(() => {
  overtimeTableData.value = tableData
  loading.value = false
})

const initFilters = () => {
  filters.value = {
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    name: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    reason: {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }],
    },
    'overtimeTime.hour': {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }],
    },
    'overtimeTime.min': {
      operator: FilterOperator.AND,
      constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }],
    },
    accepted: { value: null, matchMode: FilterMatchMode.EQUALS },
  }
}
initFilters()

const clearFilter = () => {
  initFilters()
}
</script>
