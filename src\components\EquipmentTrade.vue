<template>
  <Card>
    <template #title>
      <p style="font-weight: 900; font-size: 1.6rem">
        {{ 'Equipment' }}
      </p>
    </template>
    <template #content>
      <DataTable :value="tableData" size="large" showGridlines stripedRows sortable>
        <Column field="type" header="Type" sortable></Column>
        <Column field="compliance" header="Compliance" sortable></Column>
      </DataTable>
    </template>
  </Card>
</template>
<script setup lang="ts">
import { reactive } from 'vue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import { Card } from 'primevue'

const tradeData = [
  {
    type: 'Backhole',
    compliance: 'yes',
  },
  {
    type: 'Crane',
    compliance: 'no',
  },
  {
    type: 'Grader',
    compliance: 'yes',
  },
  {
    type: 'Vehicles',
    compliance: 'no',
  },
]

const tableData = reactive(tradeData)
</script>
