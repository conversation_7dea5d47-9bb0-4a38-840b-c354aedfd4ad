import { useMutation } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { type TrainingType, TrainingStatus } from '../graphqlTypes'

export type CreateTrainingType = {
  name: string
  description?: string | null
  startDate?: string | null
  endDate?: string | null
  duration?: string | null
  validityPeriodMonths?: number | null
  trainingType?: string | null
  trainer?: string | null
  frequency?: string | null
  status?: TrainingStatus
  workerIds?: number[] | null
}

export function useCreateTraining() {
  const query = gql`
    mutation CreateTraining(
      $name: String!
      $description: String
      $startDate: DateTime
      $endDate: DateTime
      $duration: String
      $validityPeriodMonths: Int
      $trainingType: String
      $trainer: String
      $frequency: String
      $status: TrainingStatus
      $workerIds: [Int!]
    ) {
      createTraining(
        name: $name
        description: $description
        startDate: $startDate
        endDate: $endDate
        duration: $duration
        validityPeriodMonths: $validityPeriodMonths
        trainingType: $trainingType
        trainer: $trainer
        frequency: $frequency
        status: $status
        workerIds: $workerIds
      ) {
        id
        name
        description
        startDate
        endDate
        duration
        validityPeriodMonths
        trainingType
        trainer
        frequency
        status
        createdAt
        createdBy
        updatedAt
        updatedBy
      }
    }
  `
  return useMutation<{ createTraining: TrainingType }>(query)
}
