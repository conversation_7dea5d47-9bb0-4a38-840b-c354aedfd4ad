import { useMutation } from '@vue/apollo-composable'
import gql from 'graphql-tag'
import { type WorkerType } from '../graphqlTypes'
// import { type UseMutationOptions } from '@vue/apollo-composable'

export type CreateWorkerRequestType = {
  name: string
  company: string
  nationalId: string
  gender: string
  dateOfBirth?: Date
  trainingIds: number[]
  tradeIds: number[]
  skillIds: number[]
  manHours: number
  // rating: number
  phoneNumber?: string
  email?: string
  inductionDate?: Date
  medicalCheckDate?: Date
}

export function useCreateWorker() {
  const mutationQL = gql`
    mutation CreateWorker(
      $name: String!
      $company: String!
      $nationalId: String!
      $gender: String!
      $dateOfBirth: Date
      $trainingIds: [Int!]
      $tradeIds: [Int!]
      $skillIds: [Int!]
      $manHours: Int!
      $phoneNumber: String!
      $email: String
      $inductionDate: DateTime
      $medicalCheckDate: DateTime
    ) {
      createWorker(
        name: $name
        company: $company
        nationalId: $nationalId
        gender: $gender
        dateOfBirth: $dateOfBirth
        trainingIds: $trainingIds
        tradeIds: $tradeIds
        skillIds: $skillIds
        manHours: $manHours
        phoneNumber: $phoneNumber
        email: $email
        inductionDate: $inductionDate
        medicalCheckDate: $medicalCheckDate
      ) {
        id
        name
        company
        nationalId
        gender
        dateOfBirth
        inductionDate
        medicalCheckDate
        phoneNumber
        email
        manHours
        trainingsCompleted
        trainings {
          id
          name
        }
        trades {
          id
          name
        }
        skills {
          id
          name
        }
        createdAt
        createdBy
        updatedAt
        updatedBy
      }
    }
  `
  return useMutation<{ createWorker: WorkerType }>(mutationQL)
}
