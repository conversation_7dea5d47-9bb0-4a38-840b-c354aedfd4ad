import { useMutation } from '@vue/apollo-composable'
import { type TradeType } from '../graphqlTypes'
import gql from 'graphql-tag'

export type CreateTradeType = {
  name: string
  description: string | null
  // workerIds: number[] | null
}

export function useCreateTrade() {
  const query = gql`
    mutation CreateTrade($name: String!, $description: String) # $workerIds: [Int!]
    {
      createTrade(name: $name, description: $description) # workerIds: $workerIds
      {
        #  id
        name
        #  description
        #  createdAt
        #  createdBy
        #  updatedAt
        #  updatedBy
        #  workers {
        #    id
        #    name
        #    company
        #  }
      }
    }
  `
  return useMutation<{ createTrade: TradeType }>(query)
}
