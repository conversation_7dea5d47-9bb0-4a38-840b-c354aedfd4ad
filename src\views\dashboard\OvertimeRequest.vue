<style scoped lang="scss">
.request-overtime-container {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  // border: 1px solid white;
}
.form {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  // width: fit-content;
  // border: 1px solid white;
  // height: 80%;
  // justify-content: space-between;
  padding: 0rem 1rem;
  .input-group {
    display: flex;
    justify-content: space-around;
    gap: 2rem;
  }
  .group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .btn {
    width: fit-content;
    padding-inline: 1rem;
    padding-block: 0.5rem;
  }
}
</style>
<template>
  <div class="request-overtime-container">
    <Form
      v-slot="$form"
      :resolver="resolver"
      :initialValues="initialValues"
      @submit="onFormSubmit"
      class="form"
    >
      <h1>Request Overtime</h1>
      <div class="input-group">
        <div class="group">
          <label for="overtime-name">Name</label>
          <InputText name="name" type="text" id="overtime-name" placeholder="name" fluid />
          <Message v-if="$form.name?.invalid" severity="error" size="small" variant="simple">
            {{ $form.name.error?.message }}
          </Message>
        </div>
        <div class="group">
          <label for="overtime-date">Date of overtime</label>
          <InputText name="date" id="overtime-date" type="text" placeholder="YYYY-MM-DD" fluid />
          <Message v-if="$form.date?.invalid" severity="error" size="small" variant="simple">
            {{ $form.date.error?.message }}
          </Message>
        </div>
      </div>
      <div class="group">
        <label for="overtime-reason">Reason for the overtime</label>
        <Textarea
          name="reason"
          placeholder="Reason for overtime."
          id="overtime-reason"
          rows="5"
          cols="60"
        />
        <Message v-if="$form.reason?.invalid" severity="error" size="small" variant="simple">
          {{ $form.reason.error?.message }}
        </Message>
      </div>
      <div class="input-group">
        <div class="group">
          <label for="hours">Hours of overtime</label>
          <InputNumber id="hours" name="hours" placeholder="hours of overtime." fluid />
          <Message v-if="$form.hours?.invalid" severity="error" size="small" variant="simple">
            {{ $form.hours.error?.message }}
          </Message>
        </div>
        <div class="group">
          <label for="minutes">Minutes of overtime</label>
          <InputNumber id="minutes" name="minutes" placeholder="minutes of overtime." fluid />
          <Message v-if="$form.minutes?.invalid" severity="error" size="small" variant="simple">
            {{ $form.minutes.error?.message }}
          </Message>
        </div>
      </div>
      <Button
        size="large"
        type="submit"
        class="btn"
        severity="success"
        label="Submit"
        variant="outlined"
      />
    </Form>
  </div>
</template>
<script setup lang="ts">
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Textarea from 'primevue/textarea'
import { Message, Button } from 'primevue'
import { Form } from '@primevue/forms'
import { ref } from 'vue'
import { z } from 'zod'
import { zodResolver } from '@primevue/forms/resolvers/zod'

type OvertimeFormData = {
  name: string
  date: string
  reason: string
  hours: number
  minutes: number
  // time: TimeData
}
const initialValues = ref<OvertimeFormData>({
  name: '',
  date: new Date().toJSON().split('T')[0],
  reason: '',
  hours: 0,
  minutes: 0,
})

const resolver = ref(
  zodResolver(
    z.object({
      name: z.string().min(1, { message: 'name is required.' }),
      date: z.string().date('Date is required'),
      reason: z.string(),
      hours: z
        .number()
        .nonnegative({ message: 'Must be positive value' })
        .max(10, { message: 'Must be less than 10' }),
      minutes: z
        .number()
        .nonnegative({ message: 'Must be positive value' })
        .max(59, { message: 'Must be less than 60' }),
    }),
  ),
)

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onFormSubmit = (d: any) => {
  if (d.valid) {
  }
}
</script>
