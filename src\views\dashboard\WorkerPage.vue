<style lang="scss" scoped>
.wrap-worker {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  .categories {
    height: fit-content;
    .btns {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;
      padding-bottom: 1rem;
      .btn {
        margin-block: 1rem;
      }
    }
  }
  .table {
    width: 100%;
  }
  .text {
    text-decoration: none !important;
  }
}
</style>

<template>
  <div class="wrap-worker">
    <div style="display: flex; width: 100%; padding-block: 1rem">
      <Button
        class="text"
        as="router-link"
        :to="{ name: 'createWorker' }"
        label="add worker"
        icon="pi pi-plus"
        raised
        variant="outlined"
        style="margin-left: auto"
      />
    </div>
    <div class="categories">
      <WorkerCategories />
      <div class="btns">
        <Button
          class="btn"
          size="small"
          label="Delete Changes"
          raised
          severity="danger"
          variant="outlined"
        />
        <Button class="btn" size="small" label="Save Changes" severity="success" raised />
      </div>
    </div>

    <div class="table">
      <WorkerTable />
    </div>
  </div>
</template>
<script setup lang="ts">
import { Button } from 'primevue'
import WorkerTable from '@/components/WorkerTable.vue'
import WorkerCategories from '@/components/WorkerCategories.vue'
</script>
